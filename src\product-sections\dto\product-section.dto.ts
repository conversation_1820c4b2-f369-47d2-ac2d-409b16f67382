export class ProductSectionItemDto {
  id: number;
  productId: number;
  position: number;
  product: {
    id: number;
    name: string;
    sku: string;
    price: number;
    salePrice: number | null;
    stockStatus: string;
    imageUrl: string | null;
  };
}

export class ProductSectionDto {
  id: number;
  name: string;
  position: number;
  createdAt: Date;
  updatedAt: Date;
  items: ProductSectionItemDto[];
}
