-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Add search_vector column to Product table
ALTER TABLE "Product"
  ADD COLUMN search_vector tsvector GENERATED ALWAYS AS (
    setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
    setweight(to_tsvector('english', coalesce("shortDescription", '')), 'B') ||
    setweight(to_tsvector('english', coalesce(description, '')), 'C')
  ) STORED;

-- Create GIN index for search_vector
CREATE INDEX "product_search_vector_idx"
  ON "Product"
  USING GIN(search_vector);

-- Create trigram index on name for fuzzy search
CREATE INDEX "product_name_trgm_idx"
  ON "Product"
  USING GIN (name gin_trgm_ops);
