# PowerShell script to build and push Docker image

# Configuration
$DOCKERHUB_USERNAME = "kirolosyounan"  # Replace with your Docker Hub username
$APP_NAME = "coco-jojo-backend"
$TAG = "latest"

# Create the full image name
$IMAGE_NAME = "${DOCKERHUB_USERNAME}/${APP_NAME}:${TAG}"

# Build the Docker image
Write-Host "Building Docker image: $IMAGE_NAME"
docker build -t $IMAGE_NAME .

# Check if build was successful
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Docker build failed" -ForegroundColor Red
    exit 1
}

# Push the image to Docker Hub
Write-Host "Pushing image to Docker Hub: $IMAGE_NAME"
docker push $IMAGE_NAME

# Check if push was successful
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Docker push failed. Make sure you're logged in with 'docker login'" -ForegroundColor Red
    exit 1
}

Write-Host "Successfully built and pushed Docker image: $IMAGE_NAME" -ForegroundColor Green
Write-Host "Instructions for deployment can be found in DOCKER_DEPLOYMENT.md"
