-- CreateTable
CREATE TABLE "_ProductToProductAttributeValue" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_ProductToProductAttributeValue_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_ProductToProductAttributeValue_B_index" ON "_ProductToProductAttributeValue"("B");

-- AddForeignKey
ALTER TABLE "_ProductToProductAttributeValue" ADD CONSTRAINT "_ProductToProductAttributeValue_A_fkey" FOREIGN KEY ("A") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ProductToProductAttributeValue" ADD CONSTRAINT "_ProductToProductAttributeValue_B_fkey" FOREIGN KEY ("B") REFERENCES "ProductAttributeValue"("id") ON DELETE CASCADE ON UPDATE CASCADE;
