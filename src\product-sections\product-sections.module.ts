import { Module } from '@nestjs/common';
import { ProductSectionsService } from './product-sections.service';
import { ProductSectionsController } from './product-sections.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [ProductSectionsController],
  providers: [ProductSectionsService],
  exports: [ProductSectionsService],
})
export class ProductSectionsModule {}
