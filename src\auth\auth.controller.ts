import { Controller, Post, Body, Get, UseGuards, Req } from '@nestjs/common';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { CreateAdminDto } from './dto/create-admin.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { Roles } from './decorators/roles.decorator';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superAdmin')
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('register-user')
  async registerUser(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('admin')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superAdmin')
  async createAdmin(@Body() createAdminDto: CreateAdminDto, @Req() req) {
    return this.authService.createAdmin(createAdminDto, req.user);
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  getProfile(@Req() req) {
    return req.user;
  }

  @Get('admin')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  adminRoute(@Req() req) {
    return {
      message: 'This is an admin route',
      user: req.user,
    };
  }

  @Get('super-admin')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superAdmin')
  superAdminRoute(@Req() req) {
    return {
      message: 'This is a super admin route',
      user: req.user,
    };
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  logout() {
    // JWT is stateless, so we don't need to do anything server-side
    // The client should remove the token from storage
    return { message: 'Logged out successfully' };
  }
}
