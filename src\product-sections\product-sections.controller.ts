import { Controller, Get, Post, Body, Patch, Param, Delete, ParseIntPipe } from '@nestjs/common';
import { ProductSectionsService } from './product-sections.service';
import { CreateProductSectionDto } from './dto/create-product-section.dto';
import { UpdateProductSectionDto } from './dto/update-product-section.dto';
import { AddProductToSectionDto } from './dto/add-product-to-section.dto';
import { ProductSectionDto } from './dto/product-section.dto';

@Controller('store/product-sections')
export class ProductSectionsController {
  constructor(private readonly productSectionsService: ProductSectionsService) {}

  @Post()
  create(@Body() createProductSectionDto: CreateProductSectionDto): Promise<ProductSectionDto> {
    return this.productSectionsService.create(createProductSectionDto);
  }

  @Get()
  findAll(): Promise<ProductSectionDto[]> {
    return this.productSectionsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number): Promise<ProductSectionDto> {
    return this.productSectionsService.findOne(id);
  }

  @Get('position/:position')
  findByPosition(@Param('position', ParseIntPipe) position: number): Promise<ProductSectionDto> {
    return this.productSectionsService.findByPosition(position);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProductSectionDto: UpdateProductSectionDto,
  ): Promise<ProductSectionDto> {
    return this.productSectionsService.update(id, updateProductSectionDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.productSectionsService.remove(id);
  }

  @Post(':id/products')
  addProductToSection(
    @Param('id', ParseIntPipe) id: number,
    @Body() addProductToSectionDto: AddProductToSectionDto,
  ): Promise<ProductSectionDto> {
    return this.productSectionsService.addProductToSection(id, addProductToSectionDto);
  }

  @Delete(':sectionId/products/:itemId')
  removeProductFromSection(
    @Param('sectionId', ParseIntPipe) sectionId: number,
    @Param('itemId', ParseIntPipe) itemId: number,
  ): Promise<ProductSectionDto> {
    return this.productSectionsService.removeProductFromSection(sectionId, itemId);
  }
}
