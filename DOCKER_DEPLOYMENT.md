# Coco Jojo Backend Deployment Guide

This document provides instructions for deploying the Coco Jojo Backend application using Docker.

## Prerequisites

- Docker installed on the server (version 20.10.x or later recommended)
- PostgreSQL database server already set up and running
- Network access from the Docker container to the PostgreSQL server
- Port 3000 available on the server (or another port of your choice)

## Database Requirements

- PostgreSQL 14.x or later
- The following extensions must be enabled:
  - `pg_trgm`
  - `unaccent`

You can enable these extensions by running the following SQL commands on your PostgreSQL server:

```sql
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;
```

## Deployment Steps

### 1. Pull the Docker Image

```bash
docker pull yourusername/coco-jojo-backend:latest
```

### 2. Run the Container

```bash
docker run -d \
  --name coco-jojo-backend \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e DATABASE_URL="postgresql://username:password@host:port/cocojojo?schema=public" \
  -e AUTO_APPLY_MIGRATIONS=true \
  -e JWT_SECRET=your-secret-key-here \
  yourusername/coco-jojo-backend:latest
```

Replace the following placeholders with your actual values:
- `username`: PostgreSQL username
- `password`: PostgreSQL password
- `host`: PostgreSQL server hostname or IP address
- `port`: PostgreSQL server port (usually 5432)
- `database`: PostgreSQL database name
- `your-secret-key-here`: A secure random string for JWT token signing

#### Environment Variables Explanation:
- `AUTO_APPLY_MIGRATIONS=true`: Set this to automatically apply Prisma migrations on container startup
- `JWT_SECRET`: Required for authentication - must be a secure random string

### 3. Apply Database Migrations

If you set `AUTO_APPLY_MIGRATIONS=true`, migrations will be applied automatically when the container starts.

If you prefer to run migrations manually, you can use the following command:

```bash
docker exec -it coco-jojo-backend npx prisma migrate deploy
```

### 4. Verify Deployment

Check if the container is running:

```bash
docker ps
```

Verify the application logs:

```bash
docker logs coco-jojo-backend
```

Test the API endpoint:

```bash
curl http://localhost:3000/api/health
```

## Environment Variables

You can customize the application behavior using the following environment variables:

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment mode | `production` | Yes |
| `DATABASE_URL` | PostgreSQL connection string | - | Yes |
| `PORT` | Application port | `3000` | No |
| `JWT_SECRET` | Secret key for JWT tokens | - | Yes |
| `JWT_EXPIRATION` | JWT token expiration time | `1d` | No |
| `AUTO_APPLY_MIGRATIONS` | Automatically apply Prisma migrations | `false` | No |
| `CORS_ORIGINS` | Comma-separated list of allowed origins | `*` | No |

## Troubleshooting

### Connection Issues with PostgreSQL

If the application cannot connect to the PostgreSQL database:

1. Verify that the PostgreSQL server is running
2. Check that the connection string is correct
3. Ensure that the PostgreSQL server allows connections from the Docker container's IP
4. Check the PostgreSQL server logs for any connection errors

### Container Fails to Start

If the container fails to start:

1. Check the container logs: `docker logs coco-jojo-backend`
2. Verify that all required environment variables are set correctly
3. Ensure that port 3000 is not already in use on the host

## Maintenance

### Updating the Application

To update to a newer version of the application:

```bash
# Pull the latest image
docker pull yourusername/coco-jojo-backend:latest

# Stop and remove the existing container
docker stop coco-jojo-backend
docker rm coco-jojo-backend

# Run a new container with the latest image
docker run -d \
  --name coco-jojo-backend \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e DATABASE_URL="postgresql://username:password@host:port/cocojojo?schema=public" \
  -e AUTO_APPLY_MIGRATIONS=true \
  -e JWT_SECRET=your-secret-key-here \
  yourusername/coco-jojo-backend:latest
```

**Important**: Make sure to use the same environment variables that were used when initially deploying the container.

### Backup and Restore

Database backups should be handled at the PostgreSQL server level, not through the application container.
