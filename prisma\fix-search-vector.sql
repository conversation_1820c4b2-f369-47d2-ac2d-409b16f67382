-- Check if search_vector column exists and drop it if it does
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Product'
        AND column_name = 'search_vector'
    ) THEN
        -- Drop the search_vector column
        ALTER TABLE "Product" DROP COLUMN IF EXISTS search_vector;
    END IF;
END
$$;

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Add search_vector column to Product table
ALTER TABLE "Product"
ADD COLUMN search_vector tsvector GENERATED ALWAYS AS (
    setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
    setweight(to_tsvector('english', coalesce("shortDescription", '')), 'B') ||
    setweight(to_tsvector('english', coalesce(description, '')), 'C')
) STORED;

-- Drop indexes if they exist and recreate them
DROP INDEX IF EXISTS "product_search_vector_idx";
DROP INDEX IF EXISTS "product_name_trgm_idx";

-- Create GIN index for search_vector
CREATE INDEX "product_search_vector_idx"
  ON "Product"
  USING GIN(search_vector);

-- Create trigram index on name for fuzzy search
CREATE INDEX "product_name_trgm_idx"
  ON "Product"
  USING GIN (name gin_trgm_ops);

-- Create TaxSettings table if it doesn't exist
CREATE TABLE IF NOT EXISTS "TaxSettings" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL DEFAULT 'Default Tax',
    "value" DECIMAL(10,2) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "TaxSettings_pkey" PRIMARY KEY ("id")
);

-- Insert default tax value if table is empty
INSERT INTO "TaxSettings" ("value", "updatedAt")
SELECT '0.00', CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM "TaxSettings");
