import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();

    return this.matchRoles(requiredRoles, user);
  }

  private matchRoles(roles: string[], user: any): boolean {
    if (!user) return false;

    if (roles.includes('superAdmin') && user.isSuperAdmin) {
      return true;
    }

    if (roles.includes('admin') && (user.isAdmin || user.isSuperAdmin)) {
      return true;
    }

    return false;
  }
}
