import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UpdateTaxSettingsDto } from './dto/update-tax-settings.dto';

@Injectable()
export class TaxService {
  constructor(private prisma: PrismaService) {}

  async getTaxSettings() {
    // Get the first tax settings record (we only have one)
    const taxSettings = await this.prisma.taxSettings.findFirst();
    
    if (!taxSettings) {
      // Create default tax settings if none exist
      return this.prisma.taxSettings.create({
        data: {
          name: 'Default Tax',
          value: '0.00',
          updatedAt: new Date(),
        },
      });
    }
    
    return taxSettings;
  }

  async updateTaxSettings(updateTaxSettingsDto: UpdateTaxSettingsDto) {
    const taxSettings = await this.prisma.taxSettings.findFirst();
    
    if (!taxSettings) {
      // Create tax settings if none exist
      return this.prisma.taxSettings.create({
        data: {
          name: updateTaxSettingsDto.name || 'Default Tax',
          value: updateTaxSettingsDto.value,
          updatedAt: new Date(),
        },
      });
    }
    
    // Update existing tax settings
    return this.prisma.taxSettings.update({
      where: { id: taxSettings.id },
      data: {
        ...(updateTaxSettingsDto.name && { name: updateTaxSettingsDto.name }),
        value: updateTaxSettingsDto.value,
        updatedAt: new Date(),
      },
    });
  }
}
