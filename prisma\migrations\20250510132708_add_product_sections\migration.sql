-- CreateTable
CREATE TABLE "ProductSection" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "position" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductSection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductSectionItem" (
    "id" SERIAL NOT NULL,
    "productId" INTEGER NOT NULL,
    "productSectionId" INTEGER NOT NULL,
    "position" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductSectionItem_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProductSection_position_key" ON "ProductSection"("position");

-- CreateIndex
CREATE UNIQUE INDEX "ProductSectionItem_productSectionId_position_key" ON "ProductSectionItem"("productSectionId", "position");

-- AddForeignKey
ALTER TABLE "ProductSectionItem" ADD CONSTRAINT "ProductSectionItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductSectionItem" ADD CONSTRAINT "ProductSectionItem_productSectionId_fkey" FOREIGN KEY ("productSectionId") REFERENCES "ProductSection"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Add search_vector column to Product table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'Product' AND column_name = 'search_vector') THEN
        ALTER TABLE "Product" ADD COLUMN search_vector tsvector;
    END IF;
END $$;

-- Update search_vector for existing products
UPDATE "Product" SET search_vector = to_tsvector('english', coalesce(name, '') || ' ' || coalesce(description, ''));

-- Create GIN index on search_vector if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'Product' AND indexname = 'product_search_vector_idx') THEN
        CREATE INDEX product_search_vector_idx ON "Product" USING GIN (search_vector);
    END IF;
END $$;

-- Create GIN trigram index on Product name if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'Product' AND indexname = 'product_name_trgm_idx') THEN
        CREATE INDEX product_name_trgm_idx ON "Product" USING GIN (name gin_trgm_ops);
    END IF;
END $$;
