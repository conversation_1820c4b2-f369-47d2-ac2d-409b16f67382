export class PaginationDto {
  total: number;
  page: number;
  limit: number;
}

export class ProductDto {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string | null;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: Date;
}

export class CategoryInfoDto {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

export class PaginatedProductsDto {
  pagination: PaginationDto;
  data: ProductDto[];
}

export class EnhancedPaginatedProductsDto extends PaginatedProductsDto {
  categories: CategoryInfoDto[];
}


