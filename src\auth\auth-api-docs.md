# Authentication API Documentation

This document provides detailed information about the Authentication API endpoints available in the Coco Jojo Backend.

## Base URL

All API endpoints are prefixed with `/api/auth`.

## Authentication Endpoints

### Register a New User (Admin Only)

**Note:** This endpoint is restricted to super admins only.

**Endpoint:**
```
POST /register
```

**Authorization:**
- <PERSON><PERSON> required
- Super Admin role required

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "user",
  "isAdmin": false,
  "isSuperAdmin": false,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Register a New User (Public)

Allows public registration of regular users without authentication.

**Endpoint:**
```
POST /register-user
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "user",
  "isAdmin": false,
  "isSuperAdmin": false,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Create a New Admin

Creates a new admin or super admin user. Only super admins can create new admin accounts.

**Endpoint:**
```
POST /admin
```

**Authorization:**
- Bearer Token required
- Super Admin role required

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "Admin User",
  "firstName": "Admin",
  "lastName": "User",
  "isSuperAdmin": false
}
```

**Response:**
```json
{
  "id": 2,
  "email": "<EMAIL>",
  "name": "Admin User",
  "firstName": "Admin",
  "lastName": "User",
  "isAdmin": true,
  "isSuperAdmin": false,
  "message": "Admin created successfully"
}
```

### Login

Authenticates a user and returns a JWT token.

**Endpoint:**
```
POST /login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "user",
  "isAdmin": false,
  "isSuperAdmin": false,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Get User Profile

Returns the profile information of the authenticated user.

**Endpoint:**
```
GET /profile
```

**Authorization:**
- Bearer Token required

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "isAdmin": false,
  "isSuperAdmin": false
}
```

### Admin Route

A protected route that can only be accessed by admins and super admins.

**Endpoint:**
```
GET /admin
```

**Authorization:**
- Bearer Token required
- Admin or Super Admin role required

**Response:**
```json
{
  "message": "This is an admin route",
  "user": {
    "id": 2,
    "email": "<EMAIL>",
    "isAdmin": true,
    "isSuperAdmin": false
  }
}
```

### Super Admin Route

A protected route that can only be accessed by super admins.

**Endpoint:**
```
GET /super-admin
```

**Authorization:**
- Bearer Token required
- Super Admin role required

**Response:**
```json
{
  "message": "This is a super admin route",
  "user": {
    "id": 3,
    "email": "<EMAIL>",
    "isAdmin": true,
    "isSuperAdmin": true
  }
}
```

### Logout

Logs out the current user. Since JWT is stateless, this endpoint doesn't actually invalidate the token. The client should remove the token from storage.

**Endpoint:**
```
POST /logout
```

**Authorization:**
- Bearer Token required

**Response:**
```json
{
  "message": "Logged out successfully"
}
```
