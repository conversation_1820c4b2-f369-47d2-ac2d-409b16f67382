# Shop API Documentation

This document provides examples of how to use the Shop API endpoints correctly.

## Base URL

All API endpoints are prefixed with:
```
http://localhost:3300/api/shop
```

Note: The global API prefix is `/api` and the shop controller prefix is `/shop`, resulting in all shop endpoints being prefixed with `/api/shop`.

## Main Categories

### Get All Main Categories

Retrieves all main categories with product counts.

**Endpoint:**
```
GET /main-categories
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/shop/main-categories
```

**Example Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Face Care",
      "slug": "face-care",
      "imageUrl": "https://example.com/images/face-care.jpg",
      "count": 12
    },
    {
      "id": 2,
      "name": "Hair Care",
      "slug": "hair-care",
      "imageUrl": "https://example.com/images/hair-care.jpg",
      "count": 8
    },
    {
      "id": 15,
      "name": "Essential Oils",
      "slug": "essential-oils",
      "imageUrl": "https://example.com/images/essential-oils.jpg",
      "count": 15
    }
  ]
}
```

## Subcategories

### Get Subcategories of a Main Category

Retrieves all subcategories belonging to a specific main category.

**Endpoint:**
```
GET /main-categories/:mainId/categories
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/shop/main-categories/1/categories
```

**Example Response:**
```json
{
  "mainCategory": {
    "id": 1,
    "name": "Face Care",
    "slug": "face-care"
  },
  "subcategories": [
    {
      "id": 101,
      "name": "Cleansers",
      "slug": "face-cleansers",
      "imageUrl": "https://example.com/images/cleansers.jpg",
      "count": 4,
      "parentId": 1
    },
    {
      "id": 102,
      "name": "Moisturizers",
      "slug": "face-moisturizers",
      "imageUrl": "https://example.com/images/moisturizers.jpg",
      "count": 5,
      "parentId": 1
    },
    {
      "id": 103,
      "name": "Serums",
      "slug": "face-serums",
      "imageUrl": "https://example.com/images/serums.jpg",
      "count": 3,
      "parentId": 1
    }
  ]
}
```

## Products

### Get Products by Category (Paginated)

Retrieves products belonging to a specific category with pagination. Private products are excluded from the results.

**Endpoint:**
```
GET /categories/:catId/products?page=1&limit=20
```

**Query Parameters:**
- `page` (optional, default: 1): The page number to retrieve
- `limit` (optional, default: 20): The number of products per page

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/shop/categories/101/products?page=1&limit=20
```

**Example Response:**
```json
{
  "pagination": {
    "total": 523,
    "page": 1,
    "limit": 20
  },
  "data": [
    {
      "id": 1001,
      "sku": "FC-0001",
      "name": "Gentle Foaming Cleanser",
      "slug": "gentle-foaming-cleanser",
      "imageUrl": "https://example.com/images/product1.jpg",
      "price": 24.99,
      "salePrice": null,
      "inStock": true,
      "created_at": "2023-05-15T10:30:00.000Z"
    },
    {
      "id": 1002,
      "sku": "FC-0002",
      "name": "Hydrating Cream Cleanser",
      "slug": "hydrating-cream-cleanser",
      "imageUrl": "https://example.com/images/product2.jpg",
      "price": 22.99,
      "salePrice": 19.99,
      "inStock": true,
      "created_at": "2023-05-16T14:45:00.000Z"
    }
    // More products...
  ]
}
```

### Get Product Details by Slug (Customer-Facing)

Retrieves detailed information about a specific product using its slug, including all variants, listings, tags, and other related data. Access control is applied based on the product's access level.

**Endpoint:**
```
GET /products/slug/:slug?password=optional-password
```

**Query Parameters:**
- `password` (optional): Required for protected products to access full details

**Access Control Rules:**
- Public products: Full details are always returned
- Protected products with correct password: Full details are returned
- Protected products without password or with incorrect password: Limited details are returned
- Private products: Returns 404 Not Found

**Example Request (Public Product):**
```bash
curl -X GET http://localhost:3300/api/shop/products/slug/gentle-foaming-cleanser
```

**Example Request (Protected Product with Password):**
```bash
curl -X GET "http://localhost:3300/api/shop/products/slug/protected-cleanser?password=secret123"
```

### Get Product Details by ID (Customer-Facing)

Retrieves detailed information about a specific product by ID for customer display, including all variants, listings, tags, and other related data. Access control is applied based on the product's access level. This endpoint is different from the admin-facing `/api/store/products/:id` endpoint as it enforces access control rules.

**Endpoint:**
```
GET /products/:id?password=optional-password
```

**Query Parameters:**
- `password` (optional): Required for protected products to access full details

**Access Control Rules:**
- Public products: Full details are always returned
- Protected products with correct password: Full details are returned
- Protected products without password or with incorrect password: Limited details are returned
- Private products: Returns 404 Not Found

**Example Request (Public Product):**
```bash
curl -X GET http://localhost:3300/api/shop/products/1001
```

**Example Request (Protected Product with Password):**
```bash
curl -X GET "http://localhost:3300/api/shop/products/1002?password=secret123"
```

**Example Response (Full Details):**
```json
{
  "id": 1001,
  "sku": "FC-0001",
  "name": "Gentle Foaming Cleanser",
  "slug": "gentle-foaming-cleanser",
  "description": "A gentle foaming cleanser for all skin types.",
  "shortDescription": "Gentle cleanser for all skin types",
  "price": 24.99,
  "salePrice": null,
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "type": "SIMPLE",
  "access": "PUBLIC",
  "createdAt": "2023-05-15T10:30:00.000Z",
  "updatedAt": "2023-05-15T10:30:00.000Z",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/product1-1.jpg",
      "position": 0
    },
    {
      "id": 2,
      "url": "https://example.com/images/product1-2.jpg",
      "position": 1
    }
  ],
  "categories": [
    {
      "id": 101,
      "name": "Cleansers",
      "slug": "face-cleansers",
      "imageUrl": "https://example.com/images/cleansers.jpg"
    }
  ],
  "tags": [
    {
      "id": 1,
      "name": "Gentle",
      "slug": "gentle"
    },
    {
      "id": 2,
      "name": "Foaming",
      "slug": "foaming"
    }
  ],
  "listings": [
    {
      "id": 1,
      "title": "How to Use",
      "content": "Apply to damp skin, massage gently, and rinse thoroughly."
    }
  ],
  "ProductAttribute": [
    {
      "attribute": {
        "id": 1,
        "name": "Size",
        "values": [
          {
            "id": 1,
            "value": "100ml"
          },
          {
            "id": 2,
            "value": "200ml"
          }
        ]
      },
      "values": [
        {
          "id": 1,
          "value": "100ml"
        },
        {
          "id": 2,
          "value": "200ml"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 101,
      "sku": "FC-0001-100ML",
      "price": 24.99,
      "stockQuantity": 50,
      "stockStatus": "IN_STOCK",
      "ProductImage": [
        {
          "id": 3,
          "url": "https://example.com/images/product1-100ml.jpg",
          "position": 0
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 1,
            "value": "100ml"
          }
        }
      ]
    },
    {
      "id": 102,
      "sku": "FC-0001-200ML",
      "price": 39.99,
      "stockQuantity": 50,
      "stockStatus": "IN_STOCK",
      "ProductImage": [
        {
          "id": 4,
          "url": "https://example.com/images/product1-200ml.jpg",
          "position": 0
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 2,
            "value": "200ml"
          }
        }
      ]
    }
  ]
}
```

**Example Response (Limited Details for Protected Product without Password):**
```json
{
  "id": 1002,
  "sku": "FC-0002",
  "name": "Protected Cleanser",
  "slug": "protected-cleanser",
  "description": "A protected cleanser with limited access.",
  "shortDescription": "Protected cleanser with limited access",
  "price": 29.99,
  "salePrice": null,
  "stockQuantity": 75,
  "stockStatus": "IN_STOCK",
  "type": "SIMPLE",
  "access": "PROTECTED",
  "createdAt": "2023-05-16T14:45:00.000Z",
  "updatedAt": "2023-05-16T14:45:00.000Z",
  "images": [
    {
      "id": 5,
      "url": "https://example.com/images/product2-1.jpg",
      "position": 0
    }
  ],
  "categories": [
    {
      "id": 101,
      "name": "Cleansers",
      "slug": "face-cleansers",
      "imageUrl": "https://example.com/images/cleansers.jpg"
    }
  ],
  "tags": [],
  "listings": [],
  "ProductAttribute": [],
  "variants": []
}
```

**Example Response (Private Product or Product Not Found):**
```json
{
  "message": "Product with ID 1003 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

### Get Products by Main Category and Category (Paginated)

Retrieves products belonging to a specific category within a main category. Private products are excluded from the results. The response includes both the products and all subcategories with their details.

**Endpoint:**
```
GET /shop/:mainCategorySlug/:categorySlug
```

**Path Parameters:**
- `mainCategorySlug`: The slug of the main category (e.g., "face-care")
- `categorySlug`: The slug of the category (e.g., "cleansers")

**Query Parameters:**
- `page` (optional, default: 1): The page number to retrieve
- `limit` (optional, default: 20): The number of products per page

**Example Requests:**
```bash
# Without pagination
curl -X GET http://localhost:3300/api/shop/face-care/cleansers

# With page only
curl -X GET http://localhost:3300/api/shop/face-care/cleansers?page=2

# With limit only
curl -X GET http://localhost:3300/api/shop/face-care/cleansers?limit=10

# With both pagination parameters
curl -X GET http://localhost:3300/api/shop/face-care/cleansers?page=2&limit=10
```

**Example Response:**
```json
{
  "pagination": {
    "total": 523,
    "page": 1,
    "limit": 20
  },
  "data": [
    {
      "id": 1001,
      "sku": "FC-0001",
      "name": "Gentle Foaming Cleanser",
      "slug": "gentle-foaming-cleanser",
      "imageUrl": "https://example.com/images/product1.jpg",
      "price": 24.99,
      "salePrice": null,
      "inStock": true,
      "created_at": "2023-05-15T10:30:00.000Z"
    },
    {
      "id": 1002,
      "sku": "FC-0002",
      "name": "Hydrating Cream Cleanser",
      "slug": "hydrating-cream-cleanser",
      "imageUrl": "https://example.com/images/product2.jpg",
      "price": 22.99,
      "salePrice": 19.99,
      "inStock": true,
      "created_at": "2023-05-16T14:45:00.000Z"
    }
    // More products...
  ],
  "categories": [
    {
      "id": 101,
      "name": "Cleansers",
      "slug": "face-cleansers",
      "imageUrl": "https://example.com/images/cleansers.jpg",
      "count": 4
    },
    {
      "id": 102,
      "name": "Moisturizers",
      "slug": "face-moisturizers",
      "imageUrl": "https://example.com/images/moisturizers.jpg",
      "count": 5
    },
    {
      "id": 103,
      "name": "Serums",
      "slug": "face-serums",
      "imageUrl": "https://example.com/images/serums.jpg",
      "count": 3
    }
  ]
}
```

### Get Products by Main Category (Paginated)

Retrieves all products belonging to a main category and its subcategories. Private products are excluded from the results. The response includes both the products and all subcategories with their details.

**Endpoint:**
```
GET /shop/:mainCategorySlug
```

**Path Parameters:**
- `mainCategorySlug`: The slug of the main category (e.g., "face-care")

**Query Parameters:**
- `page` (optional, default: 1): The page number to retrieve
- `limit` (optional, default: 20): The number of products per page

**Example Requests:**
```bash
# Without pagination
curl -X GET http://localhost:3300/api/shop/face-care

# With page only
curl -X GET http://localhost:3300/api/shop/face-care?page=2

# With limit only
curl -X GET http://localhost:3300/api/shop/face-care?limit=10

# With both pagination parameters
curl -X GET http://localhost:3300/api/shop/face-care?page=2&limit=10
```

**Example Response:**
```json
{
  "pagination": {
    "total": 523,
    "page": 1,
    "limit": 20
  },
  "data": [
    {
      "id": 1001,
      "sku": "FC-0001",
      "name": "Gentle Foaming Cleanser",
      "slug": "gentle-foaming-cleanser",
      "imageUrl": "https://example.com/images/product1.jpg",
      "price": 24.99,
      "salePrice": null,
      "inStock": true,
      "created_at": "2023-05-15T10:30:00.000Z"
    },
    {
      "id": 1002,
      "sku": "FC-0002",
      "name": "Hydrating Cream Cleanser",
      "slug": "hydrating-cream-cleanser",
      "imageUrl": "https://example.com/images/product2.jpg",
      "price": 22.99,
      "salePrice": 19.99,
      "inStock": true,
      "created_at": "2023-05-16T14:45:00.000Z"
    }
    // More products...
  ],
  "categories": [
    {
      "id": 101,
      "name": "Cleansers",
      "slug": "face-cleansers",
      "imageUrl": "https://example.com/images/cleansers.jpg",
      "count": 4
    },
    {
      "id": 102,
      "name": "Moisturizers",
      "slug": "face-moisturizers",
      "imageUrl": "https://example.com/images/moisturizers.jpg",
      "count": 5
    },
    {
      "id": 103,
      "name": "Serums",
      "slug": "face-serums",
      "imageUrl": "https://example.com/images/serums.jpg",
      "count": 3
    }
  ]
}
```

### Get Products by Category Slug (Paginated)

Retrieves products belonging to a specific category using the category's slug. Private products are excluded from the results.

**Endpoint:**
```
GET /categories/slug/:slug/products?page=1&limit=20
```

**Query Parameters:**
- `page` (optional, default: 1): The page number to retrieve
- `limit` (optional, default: 20): The number of products per page

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/shop/categories/slug/face-cleansers/products?page=1&limit=20
```

**Example Response:**
```json
{
  "pagination": {
    "total": 523,
    "page": 1,
    "limit": 20
  },
  "data": [
    {
      "id": 1001,
      "sku": "FC-0001",
      "name": "Gentle Foaming Cleanser",
      "slug": "gentle-foaming-cleanser",
      "imageUrl": "https://example.com/images/product1.jpg",
      "price": 24.99,
      "salePrice": null,
      "inStock": true,
      "created_at": "2023-05-15T10:30:00.000Z"
    },
    {
      "id": 1002,
      "sku": "FC-0002",
      "name": "Hydrating Cream Cleanser",
      "slug": "hydrating-cream-cleanser",
      "imageUrl": "https://example.com/images/product2.jpg",
      "price": 22.99,
      "salePrice": 19.99,
      "inStock": true,
      "created_at": "2023-05-16T14:45:00.000Z"
    }
    // More products...
  ]
}
```

### Get All Products (Paginated)

Retrieves all products in the store with pagination. Private products are excluded from the results.

**Endpoint:**
```
GET /products?page=1&limit=20
```

**Query Parameters:**
- `page` (optional, default: 1): The page number to retrieve
- `limit` (optional, default: 20): The number of products per page

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/shop/products?page=1&limit=20
```

**Example Response:**
```json
{
  "pagination": {
    "total": 523,
    "page": 1,
    "limit": 20
  },
  "data": [
    {
      "id": 1001,
      "sku": "FC-0001",
      "name": "Gentle Foaming Cleanser",
      "slug": "gentle-foaming-cleanser",
      "imageUrl": "https://example.com/images/product1.jpg",
      "price": 24.99,
      "salePrice": null,
      "inStock": true,
      "created_at": "2023-05-15T10:30:00.000Z"
    },
    {
      "id": 1002,
      "sku": "FC-0002",
      "name": "Hydrating Cream Cleanser",
      "slug": "hydrating-cream-cleanser",
      "imageUrl": "https://example.com/images/product2.jpg",
      "price": 22.99,
      "salePrice": 19.99,
      "inStock": true,
      "created_at": "2023-05-16T14:45:00.000Z"
    }
    // More products...
  ]
}
```

### Get All Product Names

Retrieves a list of all product names and their slugs, ordered alphabetically. This endpoint is optimized for SEO purposes and excludes private products. No pagination is applied to ensure all products are available for SEO indexing.

**Endpoint:**
```
GET /products/names
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/shop/products/names
```

**Example Response:**
```json
{
  "data": [
    {
      "id": 1001,
      "name": "Gentle Foaming Cleanser",
      "slug": "gentle-foaming-cleanser"
    },
    {
      "id": 1002,
      "name": "Hydrating Cream Cleanser",
      "slug": "hydrating-cream-cleanser"
    },
    {
      "id": 1003,
      "name": "Nourishing Face Serum",
      "slug": "nourishing-face-serum"
    },
    {
      "id": 1004,
      "name": "Organic Face Mask",
      "slug": "organic-face-mask"
    }
    // More products...
  ]
}
```
