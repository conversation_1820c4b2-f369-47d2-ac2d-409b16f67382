import { Test, TestingModule } from '@nestjs/testing';
import { ShopController } from './shop.controller';
import { ShopService } from './shop.service';
import { PrismaService } from '../prisma/prisma.service';

describe('ShopController', () => {
  let controller: ShopController;
  let service: ShopService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ShopController],
      providers: [
        ShopService,
        {
          provide: PrismaService,
          useValue: {
            mainCategory: {
              findMany: jest.fn(),
              findUnique: jest.fn(),
            },
            category: {
              findMany: jest.fn(),
              findUnique: jest.fn(),
            },
            productCategories: {
              count: jest.fn(),
            },
            product: {
              findMany: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    controller = module.get<ShopController>(ShopController);
    service = module.get<ShopService>(ShopService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getMainCategories', () => {
    it('should return main categories with counts', async () => {
      const result = {
        data: [
          {
            id: 1,
            name: 'Face Care',
            slug: 'face-care',
            imageUrl: 'https://example.com/face-care.jpg',
            count: 12,
          },
        ],
      };
      jest.spyOn(service, 'getMainCategories').mockResolvedValue(result);

      expect(await controller.getMainCategories()).toBe(result);
    });
  });

  describe('getSubcategories', () => {
    it('should return subcategories for a main category', async () => {
      const result = {
        mainCategory: {
          id: 1,
          name: 'Face Care',
          slug: 'face-care',
        },
        subcategories: [
          {
            id: 101,
            name: 'Cleansers',
            slug: 'face-cleansers',
            imageUrl: 'https://example.com/cleansers.jpg',
            count: 4,
            parentId: 1,
          },
        ],
      };
      jest.spyOn(service, 'getSubcategories').mockResolvedValue(result);

      expect(await controller.getSubcategories(1)).toBe(result);
    });
  });

  describe('getProductsByCategory', () => {
    it('should return paginated products for a category', async () => {
      const result = {
        pagination: {
          total: 4,
          page: 1,
          limit: 20,
        },
        data: [
          {
            id: 1001,
            sku: 'FC-0001',
            name: 'Gentle Foaming Cleanser',
            slug: 'gentle-foaming-cleanser',
            imageUrl: 'https://example.com/product1.jpg',
            price: 24.99,
            salePrice: null,
            inStock: true,
            created_at: new Date(),
          },
        ],
      };
      jest.spyOn(service, 'getProductsByCategory').mockResolvedValue(result);

      expect(await controller.getProductsByCategory(101, 1, 20)).toBe(result);
    });
  });
});
