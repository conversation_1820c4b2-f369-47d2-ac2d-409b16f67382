import { IsNotEmpty, IsString, IsOptional, IsArray, IsEnum, IsInt, IsPositive, IsDateString, IsDecimal, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { AccessLevel, ProductType, StockStatus, TaxClass, TaxStatus } from '@prisma/client';
import { CreateProductAttributeDto } from './create-product-attribute.dto';
import { CreateProductVariantDto } from './create-product-variant.dto';
import { CreateListingDto } from './create-listing.dto';

export class UpdateProductImageDto {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsString()
  url: string;

  @IsOptional()
  @IsInt()
  position?: number;

  @IsOptional()
  @IsBoolean()
  delete?: boolean;
}

export class UpdateProductAttributeValueDto {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsNotEmpty()
  @IsString()
  value: string;

  @IsOptional()
  @IsBoolean()
  delete?: boolean;
}

export class UpdateVariantDto extends CreateProductVariantDto {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsOptional()
  @IsBoolean()
  delete?: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateProductImageDto)
  images?: UpdateProductImageDto[];
}

export class UpdateProductAttributeDto extends CreateProductAttributeDto {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsOptional()
  @IsBoolean()
  delete?: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateProductAttributeValueDto)
  values?: UpdateProductAttributeValueDto[];
}

export class UpdateListingDto extends CreateListingDto {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsOptional()
  @IsBoolean()
  delete?: boolean;
}

export class UpdateGroupedProductDto {
  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  shortDescription?: string;

  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  price?: string; // Using string for Decimal

  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  salePrice?: string; // Using string for Decimal

  @IsOptional()
  @IsDateString()
  saleStart?: string;

  @IsOptional()
  @IsDateString()
  saleEnd?: string;

  @IsOptional()
  @IsInt()
  @IsPositive()
  stockQuantity?: number;

  @IsOptional()
  @IsEnum(StockStatus)
  stockStatus?: StockStatus;

  @IsOptional()
  @IsEnum(TaxStatus)
  taxStatus?: TaxStatus;

  @IsOptional()
  @IsEnum(TaxClass)
  taxClass?: TaxClass;

  @IsOptional()
  @IsEnum(AccessLevel)
  access?: AccessLevel;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  categoryIds?: number[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateProductImageDto)
  images?: UpdateProductImageDto[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  // Fields for GROUPED product type
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateProductAttributeDto)
  productAttributes?: UpdateProductAttributeDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateVariantDto)
  variants?: UpdateVariantDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateListingDto)
  listings?: UpdateListingDto[];
}
