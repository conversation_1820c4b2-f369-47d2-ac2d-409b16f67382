import { IsOptional, IsString, <PERSON><PERSON><PERSON>y, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateMainCategoryDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  categoryIds?: number[];
  
  @IsOptional()
  @IsString()
  imageUrl?: string;
}

