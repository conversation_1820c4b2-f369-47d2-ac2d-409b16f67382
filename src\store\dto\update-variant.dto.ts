import { IsArray, IsDecimal, IsInt, IsNumber, IsOptional, IsString, IsEnum } from 'class-validator';
import { StockStatus } from '@prisma/client';

export class UpdateVariantImageDto {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsString()
  url: string;

  @IsInt()
  position: number;

  @IsOptional()
  delete?: boolean;
}

export class UpdateVariantDto {
  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsDecimal()
  price?: number;

  @IsOptional()
  @IsDecimal()
  salePrice?: number;

  @IsOptional()
  saleStart?: Date;

  @IsOptional()
  saleEnd?: Date;

  @IsOptional()
  @IsInt()
  stockQuantity?: number;

  @IsOptional()
  @IsEnum(StockStatus)
  stockStatus?: StockStatus;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  attributeValueIds?: number[];

  @IsOptional()
  @IsArray()
  images?: UpdateVariantImageDto[];
} 