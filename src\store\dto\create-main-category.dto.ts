import { IsNotEmpty, IsOptional, IsString, IsArray, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateMainCategoryDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  slug: string;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  categoryIds?: number[];
  
  @IsOptional()
  @IsString()
  imageUrl?: string;
}

