# Grouped Products Update API Examples

This document provides comprehensive examples for the `PUT /api/store/grouped-products/:id` endpoint, which allows you to update grouped products with all their related data including variants, attributes, images, and more.

## Endpoint

```
PUT /api/store/grouped-products/:id
```

## Example 1: Basic Product Information Update

Update just the basic product information without touching relationships.

### Request

```json
{
  "name": "Premium T-Shirt Collection",
  "description": "Our premium collection of high-quality t-shirts",
  "shortDescription": "Premium t-shirts for all occasions",
  "price": "39.99",
  "salePrice": "29.99",
  "saleStart": "2023-07-01T00:00:00Z",
  "saleEnd": "2023-08-01T00:00:00Z",
  "stockQuantity": 150,
  "stockStatus": "IN_STOCK"
}
```

### Response

```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "Premium T-Shirt Collection",
  "description": "Our premium collection of high-quality t-shirts",
  "shortDescription": "Premium t-shirts for all occasions",
  "price": "39.99",
  "salePrice": "29.99",
  "saleStart": "2023-07-01T00:00:00Z",
  "saleEnd": "2023-08-01T00:00:00Z",
  "stockQuantity": 150,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "access": "PUBLIC",
  "slug": "t-shirt-collection-123456",
  "images": [
    // existing images remain unchanged
  ],
  "categories": [
    // existing categories remain unchanged
  ],
  "tags": [
    // existing tags remain unchanged
  ],
  "listings": [
    // existing listings remain unchanged
  ],
  "ProductAttribute": [
    // existing attributes remain unchanged
  ],
  "variants": [
    // existing variants remain unchanged
  ]
}
```

## Example 2: Update Product Images

Update, add, and delete product images.

### Request

```json
{
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/tshirt-main-updated.jpg",
      "position": 1
    },
    {
      "id": 2,
      "delete": true
    },
    {
      "url": "https://example.com/images/tshirt-new-angle.jpg",
      "position": 2
    }
  ]
}
```

### Response

```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "T-Shirt Collection",
  // other base fields...
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/tshirt-main-updated.jpg",
      "position": 1,
      "productId": 1,
      "variantId": null
    },
    {
      "id": 3,
      "url": "https://example.com/images/tshirt-new-angle.jpg",
      "position": 2,
      "productId": 1,
      "variantId": null
    }
  ],
  // other relationships remain unchanged
}
```

## Example 3: Update Categories and Tags

Update product categories and tags.

### Request

```json
{
  "categoryIds": [2, 3, 5],
  "tags": ["premium", "cotton", "summer", "casual"]
}
```

### Response

```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "T-Shirt Collection",
  // other base fields...
  "categories": [
    {
      "id": 2,
      "name": "T-Shirts"
    },
    {
      "id": 3,
      "name": "Premium Clothing"
    },
    {
      "id": 5,
      "name": "Summer Collection"
    }
  ],
  "tags": [
    {
      "id": 4,
      "name": "premium"
    },
    {
      "id": 5,
      "name": "cotton"
    },
    {
      "id": 3,
      "name": "summer"
    },
    {
      "id": 6,
      "name": "casual"
    }
  ],
  // other relationships remain unchanged
}
```

## Example 4: Update Product Attributes and Values

Update existing attributes, add new values, and delete values.

### Request

```json
{
  "productAttributes": [
    {
      "id": 1,
      "values": [
        { "id": 1, "value": "Small" },
        { "id": 2, "value": "Medium" },
        { "id": 3, "value": "Large" },
        { "value": "X-Large" }
      ]
    },
    {
      "id": 2,
      "values": [
        { "id": 4, "value": "Red" },
        { "id": 5, "value": "Blue" },
        { "id": 6, "delete": true },
        { "value": "Black" }
      ]
    },
    {
      "attributeId": 3,
      "values": [
        { "value": "100% Cotton" },
        { "value": "Polyester Blend" }
      ]
    }
  ]
}
```

### Response

```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "T-Shirt Collection",
  // other base fields...
  "ProductAttribute": [
    {
      "id": 1,
      "productId": 1,
      "attributeId": 1,
      "attribute": {
        "id": 1,
        "name": "Size"
      },
      "values": [
        {
          "id": 1,
          "productAttributeId": 1,
          "value": "Small"
        },
        {
          "id": 2,
          "productAttributeId": 1,
          "value": "Medium"
        },
        {
          "id": 3,
          "productAttributeId": 1,
          "value": "Large"
        },
        {
          "id": 7,
          "productAttributeId": 1,
          "value": "X-Large"
        }
      ]
    },
    {
      "id": 2,
      "productId": 1,
      "attributeId": 2,
      "attribute": {
        "id": 2,
        "name": "Color"
      },
      "values": [
        {
          "id": 4,
          "productAttributeId": 2,
          "value": "Red"
        },
        {
          "id": 5,
          "productAttributeId": 2,
          "value": "Blue"
        },
        {
          "id": 8,
          "productAttributeId": 2,
          "value": "Black"
        }
      ]
    },
    {
      "id": 3,
      "productId": 1,
      "attributeId": 3,
      "attribute": {
        "id": 3,
        "name": "Material"
      },
      "values": [
        {
          "id": 9,
          "productAttributeId": 3,
          "value": "100% Cotton"
        },
        {
          "id": 10,
          "productAttributeId": 3,
          "value": "Polyester Blend"
        }
      ]
    }
  ],
  // other relationships remain unchanged
}
```

## Example 6: Comprehensive Update

This example shows a comprehensive update that modifies multiple aspects of the product.

### Request

```json
{
  "name": "Premium T-Shirt Collection 2023",
  "description": "Our premium collection of high-quality t-shirts for 2023",
  "price": "44.99",
  "stockStatus": "IN_STOCK",
  "categoryIds": [2, 5, 8],
  "tags": ["premium", "2023", "summer", "limited-edition"],
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/tshirt-main-2023.jpg",
      "position": 1
    },
    {
      "url": "https://example.com/images/tshirt-collection-2023.jpg",
      "position": 2
    }
  ],
  "productAttributes": [
    {
      "id": 1,
      "values": [
        { "id": 1, "value": "Small" },
        { "id": 2, "value": "Medium" },
        { "id": 3, "value": "Large" },
        { "value": "X-Large" }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "price": "44.99",
      "stockQuantity": 20,
      "images": [
        {
          "id": 10,
          "url": "https://example.com/images/tshirt-s-red-2023.jpg",
          "position": 1
        }
      ]
    },
    {
      "sku": "GP-12345-XL-RED-2023",
      "price": "49.99",
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [7, 4],
      "images": [
        {
          "url": "https://example.com/images/tshirt-xl-red-2023.jpg",
          "position": 1
        }
      ]
    }
  ],
  "listings": [
    {
      "id": 1,
      "title": "Premium T-Shirt Collection 2023",
      "content": "Our latest premium collection of t-shirts for 2023. Limited edition designs available now."
    },
    {
      "title": "Summer 2023 T-Shirt Collection",
      "content": "Beat the heat with our premium 2023 summer t-shirt collection."
    }
  ]
}
```

### Response

```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "Premium T-Shirt Collection 2023",
  "description": "Our premium collection of high-quality t-shirts for 2023",
  "shortDescription": "T-Shirt collection with variants",
  "price": "44.99",
  "salePrice": "29.99",
  "saleStart": "2023-07-01T00:00:00Z",
  "saleEnd": "2023-08-01T00:00:00Z",
  "stockQuantity": 150,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "access": "PUBLIC",
  "slug": "t-shirt-collection-123456",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/tshirt-main-2023.jpg",
      "position": 1,
      "productId": 1,
      "variantId": null
    },
    {
      "id": 12,
      "url": "https://example.com/images/tshirt-collection-2023.jpg",
      "position": 2,
      "productId": 1,
      "variantId": null
    }
  ],
  "categories": [
    {
      "id": 2,
      "name": "T-Shirts"
    },
    {
      "id": 5,
      "name": "Summer Collection"
    },
    {
      "id": 8,
      "name": "Limited Edition"
    }
  ],
  "tags": [
    {
      "id": 4,
      "name": "premium"
    },
    {
      "id": 7,
      "name": "2023"
    },
    {
      "id": 3,
      "name": "summer"
    },
    {
      "id": 8,
      "name": "limited-edition"
    }
  ],
  "listings": [
    {
      "id": 1,
      "title": "Premium T-Shirt Collection 2023",
      "content": "Our latest premium collection of t-shirts for 2023. Limited edition designs available now.",
      "productId": 1
    },
    {
      "id": 2,
      "title": "Summer 2023 T-Shirt Collection",
      "content": "Beat the heat with our premium 2023 summer t-shirt collection.",
      "productId": 1
    }
  ],
  "ProductAttribute": [
    {
      "id": 1,
      "productId": 1,
      "attributeId": 1,
      "attribute": {
        "id": 1,
        "name": "Size"
      },
      "values": [
        {
          "id": 1,
          "productAttributeId": 1,
          "value": "Small"
        },
        {
          "id": 2,
          "productAttributeId": 1,
          "value": "Medium"
        },
        {
          "id": 3,
          "productAttributeId": 1,
          "value": "Large"
        },
        {
          "id": 7,
          "productAttributeId": 1,
          "value": "X-Large"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "sku": "GP-12345-S-RED",
      "price": "44.99",
      "stockQuantity": 20,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "id": 10,
          "url": "https://example.com/images/tshirt-s-red-2023.jpg",
          "position": 1,
          "productId": null,
          "variantId": 1
        }
      ],
      "attributes": [
        {
          "variantId": 1,
          "valueId": 1,
          "value": {
            "id": 1,
            "productAttributeId": 1,
            "value": "Small"
          }
        },
        {
          "variantId": 1,
          "valueId": 4,
          "value": {
            "id": 4,
            "productAttributeId": 2,
            "value": "Red"
          }
        }
      ]
    },
    {
      "id": 3,
      "sku": "GP-12345-XL-RED-2023",
      "price": "49.99",
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "id": 13,
          "url": "https://example.com/images/tshirt-xl-red-2023.jpg",
          "position": 1,
          "productId": null,
          "variantId": 3
        }
      ],
      "attributes": [
        {
          "variantId": 3,
          "valueId": 7,
          "value": {
            "id": 7,
            "productAttributeId": 1,
            "value": "X-Large"
          }
        },
        {
          "variantId": 3,
          "valueId": 4,
          "value": {
            "id": 4,
            "productAttributeId": 2,
            "value": "Red"
          }
        }
      ]
    }
  ]
}
```

## Example 7: Minimal Update with Specific Fields

This example shows a minimal update that only changes specific fields.

### Request

```json
{
  "salePrice": "34.99",
  "saleStart": "2023-08-01T00:00:00Z",
  "saleEnd": "2023-09-01T00:00:00Z"
}
```

### Response

```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "T-Shirt Collection",
  "description": "A collection of t-shirts in different colors and sizes",
  "shortDescription": "T-Shirt collection with variants",
  "price": "29.99",
  "salePrice": "34.99",
  "saleStart": "2023-08-01T00:00:00Z",
  "saleEnd": "2023-09-01T00:00:00Z",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  // other fields and relationships remain unchanged
}
```

## Example 8: Update Product Listings

This example shows how to update, add, and delete product listings.

### Request

```json
{
  "listings": [
    {
      "id": 1,
      "title": "Updated Listing Title",
      "content": "This is an updated content for the existing listing."
    },
    {
      "id": 2,
      "delete": true
    },
    {
      "title": "New Product Listing",
      "content": "This is a brand new listing for the product with additional marketing information."
    }
  ]
}
```

### Response

```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "T-Shirt Collection",
  // other base fields...
  "listings": [
    {
      "id": 1,
      "title": "Updated Listing Title",
      "content": "This is an updated content for the existing listing.",
      "productId": 1
    },
    {
      "id": 3,
      "title": "New Product Listing",
      "content": "This is a brand new listing for the product with additional marketing information.",
      "productId": 1
    }
  ],
  // other relationships remain unchanged
}
```

```json
{
  "variants": [
    {
      "id": 1,
      "price": "39.99",
      "stockQuantity": 25,
      "images": [
        {
          "id": 10,
          "url": "https://example.com/images/tshirt-s-red-updated.jpg",
          "position": 1
        }
      ]
    },
    {
      "id": 2,
      "delete": true
    },
    {
      "sku": "GP-12345-XL-RED",
      "price": "44.99",
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [7, 4],
      "images": [
        {
          "url": "https://example.com/images/tshirt-xl-red.jpg",
          "position": 1
        }
      ]
    }
  ]
}
```

### Response

```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "T-Shirt Collection",
  // other base fields...
  "variants": [
    {
      "id": 1,
      "sku": "GP-12345-S-RED",
      "price": "39.99",
      "stockQuantity": 25,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "id": 10,
          "url": "https://example.com/images/tshirt-s-red-updated.jpg",
          "position": 1,
          "productId": null,
          "variantId": 1
        }
      ],
      "attributes": [
        {
          "variantId": 1,
          "valueId": 1,
          "value": {
            "id": 1,
            "productAttributeId": 1,
            "value": "Small"
          }
        },
        {
          "variantId": 1,
          "valueId": 4,
          "value": {
            "id": 4,
            "productAttributeId": 2,
            "value": "Red"
          }
        }
      ]
    },
    {
      "id": 3,
      "sku": "GP-12345-XL-RED",
      "price": "44.99",
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "id": 11,
          "url": "https://example.com/images/tshirt-xl-red.jpg",
          "position": 1,
          "productId": null,
          "variantId": 3
        }
      ],
      "attributes": [
        {
          "variantId": 3,
          "valueId": 7,
          "value": {
            "id": 7,
            "productAttributeId": 1,
            "value": "X-Large"
          }
        },
        {
          "variantId": 3,
          "valueId": 4,
          "value": {
            "id": 4,
            "productAttributeId": 2,
            "value": "Red"
          }
        }
      ]
    }
  ],
  // other relationships remain unchanged
}
```
