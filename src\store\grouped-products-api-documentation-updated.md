# Grouped Products API Documentation (Updated)

This document provides detailed information about the Grouped Products API endpoints available in the Coco Jojo Backend, reflecting the updated schema without attribute type and price modifiers.

## Base URL

All API endpoints are prefixed with `/store`.

## Attribute Management

### Create Attribute

Creates a new attribute that can be used with grouped products.

**Endpoint:**
```
POST /grouped-products/attributes
```

**Request Body:**
```json
{
  "name": "Size",
  "values": [
    {
      "value": "Small"
    },
    {
      "value": "Medium"
    },
    {
      "value": "Large"
    }
  ]
}
```

**Response:**
```json
{
  "id": 1,
  "name": "Size"
}
```

### Get All Attributes

Retrieves a list of all available attributes.

**Endpoint:**
```
GET /grouped-products/attributes
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "Size"
  },
  {
    "id": 2,
    "name": "Color"
  }
]
```

### Get Attribute by ID

Retrieves a specific attribute by its ID.

**Endpoint:**
```
GET /grouped-products/attributes/:id
```

**Response:**
```json
{
  "id": 1,
  "name": "Size"
}
```

### Get Attribute Values

Retrieves all values associated with a specific attribute.

**Endpoint:**
```
GET /grouped-products/attributes/:id/values
```

**Response:**
```json
[
  {
    "id": 1,
    "value": "Small"
  },
  {
    "id": 2,
    "value": "Medium"
  },
  {
    "id": 3,
    "value": "Large"
  }
]
```

### Add Value to Attribute

Adds a new value directly to an attribute.

**Endpoint:**
```
POST /grouped-products/attributes/:id/values
```

**Request Body:**
```json
{
  "value": "Extra Large"
}
```

**Response:**
```json
{
  "id": 4,
  "value": "Extra Large",
  "attributeId": 1
}
```

## Product Attribute Management

### Add Attribute to Product

Associates an existing attribute with a product.

**Endpoint:**
```
POST /grouped-products/products/:productId/attributes/:attributeId
```

**Response:**
```json
{
  "id": 1,
  "productId": 1,
  "attributeId": 1,
  "values": []
}
```

### Add Attribute Value

Adds a value to a product attribute.

**Endpoint:**
```
POST /grouped-products/product-attributes/:id/values
```

**Request Body:**
```json
{
  "value": "Small"
}
```

**Response:**
```json
{
  "id": 1,
  "productAttributeId": 1,
  "value": "Small"
}
```

### Get Product Attributes

Retrieves all attributes associated with a specific product.

**Endpoint:**
```
GET /grouped-products/products/:id/attributes
```

**Response:**
```json
[
  {
    "id": 1,
    "productId": 1,
    "attributeId": 1,
    "attribute": {
      "id": 1,
      "name": "Size"
    },
    "values": [
      {
        "id": 1,
        "productAttributeId": 1,
        "value": "Small"
      },
      {
        "id": 2,
        "productAttributeId": 1,
        "value": "Medium"
      },
      {
        "id": 3,
        "productAttributeId": 1,
        "value": "Large"
      }
    ]
  },
  {
    "id": 2,
    "productId": 1,
    "attributeId": 2,
    "attribute": {
      "id": 2,
      "name": "Color"
    },
    "values": [
      {
        "id": 4,
        "productAttributeId": 2,
        "value": "Red"
      },
      {
        "id": 5,
        "productAttributeId": 2,
        "value": "Blue"
      }
    ]
  }
]
```

## Creating Grouped Products

Grouped products can be created using the standard product creation endpoint with additional fields.

**Endpoint:**
```
POST /products
```

**Request Body:**
```json
{
  "sku": "TSHIRT-GROUP",
  "name": "Premium T-Shirt Collection",
  "description": "Our premium t-shirt collection with various options",
  "shortDescription": "Premium t-shirts in various sizes and colors",
  "price": "29.99",
  "stockStatus": "IN_STOCK",
  "type": "GROUPED",
  "categoryIds": [1],
  "images": [
    {
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 0
    }
  ],
  "productAttributes": [
    {
      "attributeId": 1,
      "values": [
        {
          "value": "Small"
        },
        {
          "value": "Medium"
        },
        {
          "value": "Large"
        }
      ]
    },
    {
      "attributeId": 2,
      "values": [
        {
          "value": "Red"
        },
        {
          "value": "Blue"
        },
        {
          "value": "Black"
        }
      ]
    }
  ],
  "variants": [
    {
      "sku": "TSHIRT-S-RED",
      "price": "29.99",
      "stockQuantity": 10,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [1, 4],
      "images": [
        {
          "url": "https://example.com/images/tshirt-s-red.jpg",
          "position": 0
        }
      ]
    },
    {
      "sku": "TSHIRT-M-BLUE",
      "price": "29.99",
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [2, 5],
      "images": [
        {
          "url": "https://example.com/images/tshirt-m-blue.jpg",
          "position": 0
        }
      ]
    }
  ]
}
```

## Updating Grouped Products

Grouped products can be updated using the standard product update endpoint with additional fields.

**Endpoint:**
```
PATCH /products/:id
```

**Request Body:**
```json
{
  "name": "Premium T-Shirt Collection 2023",
  "price": "34.99",
  "productAttributes": [
    {
      "attributeId": 3,
      "values": [
        {
          "value": "Cotton"
        },
        {
          "value": "Polyester"
        }
      ]
    }
  ],
  "variants": [
    {
      "sku": "TSHIRT-S-RED-COTTON",
      "price": "34.99",
      "stockQuantity": 25,
      "attributeValueIds": [1, 4, 7],
      "images": [
        {
          "url": "https://example.com/images/tshirt-s-red-cotton.jpg",
          "position": 0
        }
      ]
    }
  ],
  "deleteVariantIds": [2],
  "deleteProductAttributeIds": []
}
```

## Retrieving Grouped Products

Grouped products can be retrieved using the standard product endpoints. The response will include all product attributes, values, and variants.

### Get All Products

**Endpoint:**
```
GET /products
```

### Get Product by ID

**Endpoint:**
```
GET /products/:id
```

### Get Products by Category

**Endpoint:**
```
GET /categories/:categoryId/products
```

## Deleting Grouped Products

Grouped products can be deleted using the standard product deletion endpoint. This will automatically delete all related attributes, values, and variants.

**Endpoint:**
```
DELETE /products/:id
```
