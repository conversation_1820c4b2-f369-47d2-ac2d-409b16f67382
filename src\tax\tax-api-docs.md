# Tax Settings API Documentation

This document provides detailed information about the Tax Settings API endpoints available in the Coco Jojo Backend.

## Base URL

All API endpoints are prefixed with `/api/tax`.

## Tax Settings Endpoints

### Get Tax Settings

Retrieves the current tax settings.

**Endpoint:**
```
GET /
```

**Response:**
```json
{
  "id": 1,
  "name": "Default Tax",
  "value": "10.00",
  "createdAt": "2025-05-18T12:00:00.000Z",
  "updatedAt": "2025-05-18T12:00:00.000Z"
}
```

### Update Tax Settings

Updates the tax settings. Only admins and super admins can update tax settings.

**Endpoint:**
```
PUT /
```

**Authorization:**
- <PERSON><PERSON> Token required
- Admin or Super Admin role required

**Request Body:**
```json
{
  "name": "Standard VAT",
  "value": "20.00"
}
```

**Response:**
```json
{
  "id": 1,
  "name": "Standard VAT",
  "value": "20.00",
  "createdAt": "2025-05-18T12:00:00.000Z",
  "updatedAt": "2025-05-18T12:30:00.000Z"
}
```
