import { IsNotEmpty, IsString, IsO<PERSON>al, IsArray, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateProductAttributeValueDto {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsNotEmpty()
  @IsString()
  value: string;

  @IsOptional()
  @IsInt()
  productAttributeId?: number;
}

export class CreateProductAttributeDto {
  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  attributeId: number;

  @IsOptional()
  @IsArray()
  values?: CreateProductAttributeValueDto[];
}
