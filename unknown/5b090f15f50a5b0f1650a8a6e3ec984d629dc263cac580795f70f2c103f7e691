# Product API Documentation

## Product Management

### Create a New Product

**Endpoint:** `POST /store/products`

**Request Body:**
```json
{
  "sku": "PROD-001",
  "name": "Smartphone X",
  "description": "Latest smartphone with advanced features",
  "shortDescription": "Latest smartphone model",
  "price": "999.99",
  "salePrice": "899.99",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "categoryIds": [1, 2],
  "images": [
    {
      "url": "https://example.com/images/smartphone-1.jpg",
      "position": 0
    },
    {
      "url": "https://example.com/images/smartphone-2.jpg",
      "position": 1
    }
  ]
}
```

### Create a New Product for a Specific Category

**Endpoint:** `POST /store/categories/:categoryId/products`

**Request Body:**
```json
{
  "sku": "PROD-002",
  "name": "Skin Cream",
  "description": "Moisturizing skin cream",
  "shortDescription": "Hydrating cream",
  "price": "24.99",
  "stockQuantity": 50,
  "images": [
    {
      "url": "https://example.com/images/skin-cream.jpg",
      "position": 0
    }
  ]
}
```

**Note:** The product will be automatically associated with the specified category.

**Response (201 Created):**
```json
{
  "id": 1,
  "sku": "PROD-001",
  "name": "Smartphone X",
  "description": "Latest smartphone with advanced features",
  "shortDescription": "Latest smartphone model",
  "price": "999.99",
  "salePrice": "899.99",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T12:00:00Z",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/smartphone-1.jpg",
      "position": 0,
      "productId": 1
    },
    {
      "id": 2,
      "url": "https://example.com/images/smartphone-2.jpg",
      "position": 1,
      "productId": 1
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Electronics",
      "slug": "electronics",
      "description": "Electronic devices and accessories"
    },
    {
      "id": 2,
      "name": "Smartphones",
      "slug": "smartphones",
      "description": "Mobile phones and accessories"
    }
  ]
}
```

### Get All Products

**Endpoint:** `GET /store/products`

**Response (200 OK):**
```json
[
  {
    "id": 1,
    "name": "Smartphone X",
    "shortDescription": "Latest smartphone model",
    "price": "999.99",
    "images": [
      {
        "url": "https://example.com/images/smartphone-1.jpg"
      }
    ]
  },
  {
    "id": 2,
    "name": "Laptop Pro",
    "shortDescription": "Professional laptop for developers",
    "price": "1499.99",
    "images": [
      {
        "url": "https://example.com/images/laptop-1.jpg"
      }
    ]
  }
]
```

### Get Product by ID

**Endpoint:** `GET /store/products/:id`

**Response (200 OK):**
```json
{
  "id": 1,
  "sku": "PROD-001",
  "name": "Smartphone X",
  "description": "Latest smartphone with advanced features",
  "shortDescription": "Latest smartphone model",
  "price": "999.99",
  "salePrice": "899.99",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T12:00:00Z",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/smartphone-1.jpg",
      "position": 0,
      "productId": 1
    },
    {
      "id": 2,
      "url": "https://example.com/images/smartphone-2.jpg",
      "position": 1,
      "productId": 1
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Electronics",
      "slug": "electronics",
      "description": "Electronic devices and accessories"
    },
    {
      "id": 2,
      "name": "Smartphones",
      "slug": "smartphones",
      "description": "Mobile phones and accessories"
    }
  ]
}
```

### Get Products by Category

**Endpoint:** `GET /store/categories/:categoryId/products`

**Response (200 OK):**
```json
[
  {
    "id": 1,
    "name": "Smartphone X",
    "shortDescription": "Latest smartphone model",
    "price": "999.99",
    "images": [
      {
        "url": "https://example.com/images/smartphone-1.jpg"
      }
    ]
  },
  {
    "id": 3,
    "name": "Tablet Ultra",
    "shortDescription": "Ultra-thin tablet with high resolution",
    "price": "699.99",
    "images": [
      {
        "url": "https://example.com/images/tablet-1.jpg"
      }
    ]
  }
]
```

### Update Product

**Endpoint:** `PATCH /store/products/:id`

**Request Body:**
```json
{
  "name": "Smartphone X Pro",
  "price": "1099.99",
  "salePrice": "999.99",
  "categoryIds": [1, 2, 3]
}
```

**Response (200 OK):**
```json
{
  "id": 1,
  "sku": "PROD-001",
  "name": "Smartphone X Pro",
  "description": "Latest smartphone with advanced features",
  "shortDescription": "Latest smartphone model",
  "price": "1099.99",
  "salePrice": "999.99",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T13:00:00Z",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/smartphone-1.jpg",
      "position": 0,
      "productId": 1
    },
    {
      "id": 2,
      "url": "https://example.com/images/smartphone-2.jpg",
      "position": 1,
      "productId": 1
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Electronics",
      "slug": "electronics",
      "description": "Electronic devices and accessories"
    },
    {
      "id": 2,
      "name": "Smartphones",
      "slug": "smartphones",
      "description": "Mobile phones and accessories"
    },
    {
      "id": 3,
      "name": "Premium",
      "slug": "premium",
      "description": "Premium products"
    }
  ]
}
```

### Delete Product

**Endpoint:** `DELETE /store/products/:id`

**Response (200 OK):**
```json
{
  "id": 1,
  "sku": "PROD-001",
  "name": "Smartphone X Pro",
  "description": "Latest smartphone with advanced features",
  "shortDescription": "Latest smartphone model",
  "price": "1099.99",
  "salePrice": "999.99",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T13:00:00Z",
  "images": [],
  "categories": []
}
```
