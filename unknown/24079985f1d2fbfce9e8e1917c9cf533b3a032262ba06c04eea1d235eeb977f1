/*
  Warnings:

  - You are about to drop the `_VariantImages` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "AttributeType" AS ENUM ('PRIMARY', 'SECONDARY');

-- DropForeignKey
ALTER TABLE "ProductImage" DROP CONSTRAINT "ProductImage_productId_fkey";

-- DropForeignKey
ALTER TABLE "_VariantImages" DROP CONSTRAINT "_VariantImages_A_fkey";

-- DropForeignKey
ALTER TABLE "_VariantImages" DROP CONSTRAINT "_VariantImages_B_fkey";

-- AlterTable
ALTER TABLE "Attribute" ADD COLUMN     "type" "AttributeType" NOT NULL DEFAULT 'SECONDARY';

-- AlterTable
ALTER TABLE "AttributeValue" ADD COLUMN     "priceModifier" DECIMAL(10,2);

-- AlterTable
ALTER TABLE "ProductImage" ADD COLUMN     "variantId" INTEGER,
ALTER COLUMN "productId" DROP NOT NULL;

-- DropTable
DROP TABLE "_VariantImages";

-- CreateTable
CREATE TABLE "ProductAttribute" (
    "id" SERIAL NOT NULL,
    "productId" INTEGER NOT NULL,
    "attributeId" INTEGER NOT NULL,

    CONSTRAINT "ProductAttribute_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ProductImage" ADD CONSTRAINT "ProductImage_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductImage" ADD CONSTRAINT "ProductImage_variantId_fkey" FOREIGN KEY ("variantId") REFERENCES "ProductVariant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAttribute" ADD CONSTRAINT "ProductAttribute_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAttribute" ADD CONSTRAINT "ProductAttribute_attributeId_fkey" FOREIGN KEY ("attributeId") REFERENCES "Attribute"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
