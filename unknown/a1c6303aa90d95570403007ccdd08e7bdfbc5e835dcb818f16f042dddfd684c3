# Auth API Documentation

This document provides examples of how to use the Auth API endpoints correctly.

## Base URL

All API endpoints are prefixed with:
```
http://localhost:3300/api/auth
```

## Authentication Endpoints

### Register a New User

**Endpoint:**
```
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

**Example Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "user",
  "isAdmin": false,
  "isSuperAdmin": false,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Login

**Endpoint:**
```
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

**Example Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "user",
  "isAdmin": false,
  "isSuperAdmin": false,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Get User Profile

**Endpoint:**
```
GET /auth/profile
```

**Headers:**
```
Authorization: Bearer <access_token>
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/auth/profile \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Example Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "isAdmin": false,
  "isSuperAdmin": false
}
```

### Admin Route (Requires Admin Role)

**Endpoint:**
```
GET /auth/admin
```

**Headers:**
```
Authorization: Bearer <access_token>
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/auth/admin \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Example Response (for admin user):**
```json
{
  "message": "This is an admin route",
  "user": {
    "id": 2,
    "email": "<EMAIL>",
    "isAdmin": true,
    "isSuperAdmin": false
  }
}
```

### Super Admin Route (Requires Super Admin Role)

**Endpoint:**
```
GET /auth/super-admin
```

**Headers:**
```
Authorization: Bearer <access_token>
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/auth/super-admin \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Example Response (for super admin user):**
```json
{
  "message": "This is a super admin route",
  "user": {
    "id": 3,
    "email": "<EMAIL>",
    "isAdmin": false,
    "isSuperAdmin": true
  }
}
```

### Logout

**Endpoint:**
```
POST /auth/logout
```

**Headers:**
```
Authorization: Bearer <access_token>
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/auth/logout \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Example Response:**
```json
{
  "message": "Logged out successfully"
}
```

## Using JWT Authentication in Other Endpoints

To protect other endpoints in your application, you can use the JwtAuthGuard:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('some-resource')
export class SomeController {
  @UseGuards(JwtAuthGuard)
  @Get()
  getProtectedResource() {
    return { message: 'This is a protected resource' };
  }
}
```

For role-based access control, you can use the RolesGuard along with the Roles decorator:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@Controller('admin-resource')
export class AdminController {
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Get()
  getAdminResource() {
    return { message: 'This is an admin-only resource' };
  }
}
```
