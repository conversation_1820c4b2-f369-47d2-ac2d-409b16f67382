# Store API Documentation

## Category Management

### Create a New Category

**Endpoint:** `POST /store/categories`

**Request Body:**
```json
{
  "name": "Electronics",
  "slug": "electronics",
  "description": "Electronic devices and accessories"
}
```

**Response (201 Created):**
```json
{
  "id": 1,
  "name": "Electronics",
  "slug": "electronics",
  "description": "Electronic devices and accessories"
}
```

### Get All Categories

**Endpoint:** `GET /store/categories`

**Response (200 OK):**
```json
[
  {
    "id": 1,
    "name": "Electronics",
    "slug": "electronics",
    "description": "Electronic devices and accessories"
  },
  {
    "id": 2,
    "name": "Clothing",
    "slug": "clothing",
    "description": "Fashion items and apparel"
  }
]
```

### Get Category by ID

**Endpoint:** `GET /store/categories/1`

**Response (200 OK):**
```json
{
  "id": 1,
  "name": "Electronics",
  "slug": "electronics",
  "description": "Electronic devices and accessories"
}
```

**Response (404 Not Found):**
```json
{
  "statusCode": 404,
  "message": "Category with ID 999 not found",
  "error": "Not Found"
}
```

### Update Category

**Endpoint:** `PATCH /store/categories/1`

**Request Body:**
```json
{
  "name": "Updated Electronics",
  "description": "Updated description for electronic products"
}
```

**Response (200 OK):**
```json
{
  "id": 1,
  "name": "Updated Electronics",
  "slug": "electronics",
  "description": "Updated description for electronic products"
}
```

**Response (404 Not Found):**
```json
{
  "statusCode": 404,
  "message": "Category with ID 999 not found",
  "error": "Not Found"
}
```

### Delete Category

**Endpoint:** `DELETE /store/categories/1`

**Response (200 OK):**
```json
{
  "id": 1,
  "name": "Electronics",
  "slug": "electronics",
  "description": "Electronic devices and accessories"
}
```

**Response (404 Not Found):**
```json
{
  "statusCode": 404,
  "message": "Category with ID 999 not found",
  "error": "Not Found"
}
```
