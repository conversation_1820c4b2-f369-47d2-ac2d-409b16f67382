# Grouped Products API Documentation

This document provides detailed information about the Grouped Products API endpoints available in the Coco Jojo Backend.

## Base URL

All API endpoints are prefixed with `/api/store`.

## Attribute Management

### Create Attribute

Creates a new attribute that can be used with grouped products.

**Endpoint:**
```
POST /grouped-products/attributes
```

**Request Body:**
```json
{
  "name": "Size",
  "values": [
    {
      "value": "Small"
    },
    {
      "value": "Medium"
    },
    {
      "value": "Large"
    }
  ]
}
```

**Response:**
```json
{
  "id": 1,
  "name": "Size"
}
```

### Get All Attributes

Retrieves all available attributes.

**Endpoint:**
```
GET /grouped-products/attributes
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "Size"
  },
  {
    "id": 2,
    "name": "Color"
  }
]
```

### Get Attribute by ID

Retrieves a specific attribute by its ID.

**Endpoint:**
```
GET /grouped-products/attributes/:id
```

**Response:**
```json
{
  "id": 1,
  "name": "Size"
}
```

## Grouped Product Management

### Create Grouped Product

Creates a new grouped product with attributes, attribute values, and variants.

**Endpoint:**
```
POST /grouped-products
```

**Request Body:**
```json
{
  "sku": "TSHIRT-GROUP",
  "name": "Premium T-Shirt Collection",
  "description": "Our premium t-shirt collection with various options",
  "shortDescription": "Premium t-shirts in various sizes and colors",
  "price": "29.99",
  "stockStatus": "IN_STOCK",
  "categoryIds": [1, 2],
  "images": [
    {
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 0
    }
  ],
  "productAttributes": [
    {
      "attributeId": 1,
      "values": [
        {
          "value": "Small"
        },
        {
          "value": "Medium"
        },
        {
          "value": "Large"
        }
      ]
    },
    {
      "attributeId": 2,
      "values": [
        {
          "value": "Red"
        },
        {
          "value": "Blue"
        },
        {
          "value": "Black"
        }
      ]
    }
  ],
  "variants": [
    {
      "sku": "TSHIRT-S-RED",
      "price": "29.99",
      "stockQuantity": 10,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [1, 4],
      "images": [
        {
          "url": "https://example.com/images/tshirt-s-red.jpg",
          "position": 0
        }
      ]
    },
    {
      "sku": "TSHIRT-M-BLUE",
      "price": "29.99",
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [2, 5],
      "images": [
        {
          "url": "https://example.com/images/tshirt-m-blue.jpg",
          "position": 0
        }
      ]
    },
    {
      "sku": "TSHIRT-L-BLACK",
      "price": "34.99",
      "stockQuantity": 8,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [3, 6],
      "images": [
        {
          "url": "https://example.com/images/tshirt-l-black.jpg",
          "position": 0
        }
      ]
    }
  ]
}
```

**Response:**
```json
{
  "id": 1,
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T12:00:00Z",
  "sku": "TSHIRT-GROUP",
  "name": "Premium T-Shirt Collection",
  "description": "Our premium t-shirt collection with various options",
  "shortDescription": "Premium t-shirts in various sizes and colors",
  "price": "29.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": null,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 0,
      "productId": 1,
      "variantId": null
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Clothing",
      "slug": "clothing",
      "description": "Clothing items"
    },
    {
      "id": 2,
      "name": "T-Shirts",
      "slug": "t-shirts",
      "description": "T-Shirt collection"
    }
  ],
  "ProductAttribute": [
    {
      "id": 1,
      "productId": 1,
      "attributeId": 1,
      "attribute": {
        "id": 1,
        "name": "Size"
      },
      "values": [
        {
          "id": 1,
          "productAttributeId": 1,
          "value": "Small"
        },
        {
          "id": 2,
          "productAttributeId": 1,
          "value": "Medium"
        },
        {
          "id": 3,
          "productAttributeId": 1,
          "value": "Large"
        }
      ]
    },
    {
      "id": 2,
      "productId": 1,
      "attributeId": 2,
      "attribute": {
        "id": 2,
        "name": "Color"
      },
      "values": [
        {
          "id": 4,
          "productAttributeId": 2,
          "value": "Red"
        },
        {
          "id": 5,
          "productAttributeId": 2,
          "value": "Blue"
        },
        {
          "id": 6,
          "productAttributeId": 2,
          "value": "Black"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "sku": "TSHIRT-S-RED",
      "price": "29.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 10,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "url": "https://example.com/images/tshirt-s-red.jpg"
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 1,
            "value": "Small"
          }
        },
        {
          "value": {
            "id": 4,
            "value": "Red"
          }
        }
      ]
    },
    {
      "id": 2,
      "sku": "TSHIRT-M-BLUE",
      "price": "29.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "url": "https://example.com/images/tshirt-m-blue.jpg"
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 2,
            "value": "Medium"
          }
        },
        {
          "value": {
            "id": 5,
            "value": "Blue"
          }
        }
      ]
    },
    {
      "id": 3,
      "sku": "TSHIRT-L-BLACK",
      "price": "34.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 8,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "url": "https://example.com/images/tshirt-l-black.jpg"
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 3,
            "value": "Large"
          }
        },
        {
          "value": {
            "id": 6,
            "value": "Black"
          }
        }
      ]
    }
  ]
}
```

### Create Grouped Product for Specific Category

Creates a new grouped product and associates it with a specific category.

**Endpoint:**
```
POST /grouped-products/categories/:categoryId
```

**Request Body:**
```json
{
  "sku": "JEANS-GROUP",
  "name": "Designer Jeans Collection",
  "description": "Our premium jeans collection with various styles and sizes",
  "shortDescription": "Premium jeans in various styles",
  "price": "59.99",
  "stockStatus": "IN_STOCK",
  "images": [
    {
      "url": "https://example.com/images/jeans-main.jpg",
      "position": 0
    }
  ],
  "productAttributes": [
    {
      "attributeId": 1,
      "values": [
        {
          "value": "Small"
        },
        {
          "value": "Medium"
        },
        {
          "value": "Large"
        }
      ]
    },
    {
      "attributeId": 3,
      "values": [
        {
          "value": "Slim Fit"
        },
        {
          "value": "Regular Fit"
        },
        {
          "value": "Relaxed Fit"
        }
      ]
    }
  ],
  "variants": [
    {
      "sku": "JEANS-S-SLIM",
      "price": "59.99",
      "stockQuantity": 12,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [1, 7],
      "images": [
        {
          "url": "https://example.com/images/jeans-s-slim.jpg",
          "position": 0
        }
      ]
    },
    {
      "sku": "JEANS-M-REGULAR",
      "price": "59.99",
      "stockQuantity": 18,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [2, 8],
      "images": [
        {
          "url": "https://example.com/images/jeans-m-regular.jpg",
          "position": 0
        }
      ]
    }
  ]
}
```

**Response:**
```json
{
  "id": 2,
  "createdAt": "2023-06-01T13:00:00Z",
  "updatedAt": "2023-06-01T13:00:00Z",
  "sku": "JEANS-GROUP",
  "name": "Designer Jeans Collection",
  "description": "Our premium jeans collection with various styles and sizes",
  "shortDescription": "Premium jeans in various styles",
  "price": "59.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": null,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "images": [
    {
      "id": 4,
      "url": "https://example.com/images/jeans-main.jpg",
      "position": 0,
      "productId": 2,
      "variantId": null
    }
  ],
  "categories": [
    {
      "id": 3,
      "name": "Jeans",
      "slug": "jeans",
      "description": "Jeans collection"
    }
  ],
  "ProductAttribute": [
    {
      "id": 3,
      "productId": 2,
      "attributeId": 1,
      "attribute": {
        "id": 1,
        "name": "Size"
      },
      "values": [
        {
          "id": 7,
          "productAttributeId": 3,
          "value": "Small"
        },
        {
          "id": 8,
          "productAttributeId": 3,
          "value": "Medium"
        },
        {
          "id": 9,
          "productAttributeId": 3,
          "value": "Large"
        }
      ]
    },
    {
      "id": 4,
      "productId": 2,
      "attributeId": 3,
      "attribute": {
        "id": 3,
        "name": "Fit"
      },
      "values": [
        {
          "id": 10,
          "productAttributeId": 4,
          "value": "Slim Fit"
        },
        {
          "id": 11,
          "productAttributeId": 4,
          "value": "Regular Fit"
        },
        {
          "id": 12,
          "productAttributeId": 4,
          "value": "Relaxed Fit"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 4,
      "sku": "JEANS-S-SLIM",
      "price": "59.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 12,
      "stockStatus": "IN_STOCK",
      "productId": 2,
      "ProductImage": [
        {
          "url": "https://example.com/images/jeans-s-slim.jpg"
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 7,
            "value": "Small"
          }
        },
        {
          "value": {
            "id": 10,
            "value": "Slim Fit"
          }
        }
      ]
    },
    {
      "id": 5,
      "sku": "JEANS-M-REGULAR",
      "price": "59.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 18,
      "stockStatus": "IN_STOCK",
      "productId": 2,
      "ProductImage": [
        {
          "url": "https://example.com/images/jeans-m-regular.jpg"
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 8,
            "value": "Medium"
          }
        },
        {
          "value": {
            "id": 11,
            "value": "Regular Fit"
          }
        }
      ]
    }
  ]
}
```

### Get Grouped Product by ID

Retrieves a grouped product by its ID, including all attributes, attribute values, and variants.

**Endpoint:**
```
GET /grouped-products/:id
```

**Response:**
```json
{
  "id": 1,
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T12:00:00Z",
  "sku": "TSHIRT-GROUP",
  "name": "Premium T-Shirt Collection",
  "description": "Our premium t-shirt collection with various options",
  "shortDescription": "Premium t-shirts in various sizes and colors",
  "price": "29.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": null,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 0,
      "productId": 1,
      "variantId": null
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Clothing",
      "slug": "clothing",
      "description": "Clothing items"
    },
    {
      "id": 2,
      "name": "T-Shirts",
      "slug": "t-shirts",
      "description": "T-Shirt collection"
    }
  ],
  "ProductAttribute": [
    {
      "id": 1,
      "productId": 1,
      "attributeId": 1,
      "attribute": {
        "id": 1,
        "name": "Size"
      },
      "values": [
        {
          "id": 1,
          "productAttributeId": 1,
          "value": "Small"
        },
        {
          "id": 2,
          "productAttributeId": 1,
          "value": "Medium"
        },
        {
          "id": 3,
          "productAttributeId": 1,
          "value": "Large"
        }
      ]
    },
    {
      "id": 2,
      "productId": 1,
      "attributeId": 2,
      "attribute": {
        "id": 2,
        "name": "Color"
      },
      "values": [
        {
          "id": 4,
          "productAttributeId": 2,
          "value": "Red"
        },
        {
          "id": 5,
          "productAttributeId": 2,
          "value": "Blue"
        },
        {
          "id": 6,
          "productAttributeId": 2,
          "value": "Black"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "sku": "TSHIRT-S-RED",
      "price": "29.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 10,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "url": "https://example.com/images/tshirt-s-red.jpg"
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 1,
            "value": "Small"
          }
        },
        {
          "value": {
            "id": 4,
            "value": "Red"
          }
        }
      ]
    },
    {
      "id": 2,
      "sku": "TSHIRT-M-BLUE",
      "price": "29.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "url": "https://example.com/images/tshirt-m-blue.jpg"
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 2,
            "value": "Medium"
          }
        },
        {
          "value": {
            "id": 5,
            "value": "Blue"
          }
        }
      ]
    },
    {
      "id": 3,
      "sku": "TSHIRT-L-BLACK",
      "price": "34.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 8,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "url": "https://example.com/images/tshirt-l-black.jpg"
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 3,
            "value": "Large"
          }
        },
        {
          "value": {
            "id": 6,
            "value": "Black"
          }
        }
      ]
    }
  ]
}
```

### Delete Grouped Product

Deletes a grouped product and all its related entities (attributes, attribute values, variants).

**Endpoint:**
```
DELETE /grouped-products/:id
```

**Response:**
```json
{
  "id": 1,
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T12:00:00Z",
  "sku": "TSHIRT-GROUP",
  "name": "Premium T-Shirt Collection",
  "description": "Our premium t-shirt collection with various options",
  "shortDescription": "Premium t-shirts in various sizes and colors",
  "price": "29.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": null,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "images": [],
  "categories": []
}
```

## Data Structure

### CreateGroupedProductDto

```typescript
interface CreateGroupedProductDto {
  sku: string;
  name: string;
  description?: string;
  shortDescription?: string;
  price: string;
  salePrice?: string;
  saleStart?: string;
  saleEnd?: string;
  stockQuantity?: number;
  stockStatus: StockStatus;
  taxStatus?: TaxStatus;
  taxClass?: TaxClass;
  categoryIds?: number[];
  images?: { url: string; position?: number }[];
  productAttributes?: CreateProductAttributeDto[];
  variants?: CreateProductVariantDto[];
}
```

### CreateProductAttributeDto

```typescript
interface CreateProductAttributeDto {
  attributeId: number;
  values?: CreateProductAttributeValueDto[];
}

interface CreateProductAttributeValueDto {
  value: string;
}
```

### CreateProductVariantDto

```typescript
interface CreateProductVariantDto {
  sku: string;
  price: string;
  salePrice?: string;
  stockQuantity?: number;
  stockStatus?: StockStatus;
  attributeValueIds: number[];
  images?: { url: string; position?: number }[];
}
```

## Frontend Integration

To integrate with these endpoints in your frontend application, you can use the following example code:

```javascript
// Example API service for grouped products
const API_BASE_URL = 'http://localhost:3300/api';

// Create a new grouped product
async function createGroupedProduct(productData) {
  const response = await fetch(`${API_BASE_URL}/store/grouped-products`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(productData),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Create a grouped product for a specific category
async function createGroupedProductForCategory(categoryId, productData) {
  const response = await fetch(`${API_BASE_URL}/store/grouped-products/categories/${categoryId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(productData),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Get all attributes
async function getAllAttributes() {
  const response = await fetch(`${API_BASE_URL}/store/grouped-products/attributes`);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Delete a grouped product
async function deleteGroupedProduct(productId) {
  const response = await fetch(`${API_BASE_URL}/store/grouped-products/${productId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}
```

Make sure your frontend is configured to use the correct base URL with the `/api/store` prefix for all API calls.
