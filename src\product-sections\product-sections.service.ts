import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProductSectionDto } from './dto/create-product-section.dto';
import { UpdateProductSectionDto } from './dto/update-product-section.dto';
import { AddProductToSectionDto } from './dto/add-product-to-section.dto';
import { ProductSectionDto, ProductSectionItemDto } from './dto/product-section.dto';

@Injectable()
export class ProductSectionsService {
  constructor(private prisma: PrismaService) {}

  async create(createProductSectionDto: CreateProductSectionDto): Promise<ProductSectionDto> {
    const { name, position, productIds = [] } = createProductSectionDto;

    try {
      // Check if position is already taken
      const existingSection = await this.prisma.productSection.findUnique({
        where: { position },
      });

      if (existingSection) {
        throw new ConflictException(`Position ${position} is already taken`);
      }

      // Create the product section
      const section = await this.prisma.productSection.create({
        data: {
          name,
          position,
        },
      });

      // Add products to the section if provided
      if (productIds.length > 0) {
        for (let i = 0; i < productIds.length; i++) {
          await this.prisma.productSectionItem.create({
            data: {
              productId: productIds[i],
              productSectionId: section.id,
              position: i + 1,
            },
          });
        }
      }

      // Return the created section with items
      return this.findOne(section.id);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      if (error.code === 'P2002') {
        throw new ConflictException(`Position ${position} is already taken`);
      }
      throw error;
    }
  }

  async findAll(): Promise<ProductSectionDto[]> {
    const sections = await this.prisma.productSection.findMany({
      include: {
        items: {
          include: {
            product: {
              include: {
                images: {
                  take: 1,
                },
              },
            },
          },
          orderBy: {
            position: 'asc',
          },
        },
      },
      orderBy: {
        position: 'asc',
      },
    });

    return sections.map(section => this.mapToProductSectionDto(section));
  }

  async findOne(id: number): Promise<ProductSectionDto> {
    const section = await this.prisma.productSection.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            product: {
              include: {
                images: {
                  take: 1,
                },
              },
            },
          },
          orderBy: {
            position: 'asc',
          },
        },
      },
    });

    if (!section) {
      throw new NotFoundException(`Product section with ID ${id} not found`);
    }

    return this.mapToProductSectionDto(section);
  }

  async findByPosition(position: number): Promise<ProductSectionDto> {
    const section = await this.prisma.productSection.findUnique({
      where: { position },
      include: {
        items: {
          include: {
            product: {
              include: {
                images: {
                  take: 1,
                },
              },
            },
          },
          orderBy: {
            position: 'asc',
          },
        },
      },
    });

    if (!section) {
      throw new NotFoundException(`Product section with position ${position} not found`);
    }

    return this.mapToProductSectionDto(section);
  }

  async update(id: number, updateProductSectionDto: UpdateProductSectionDto): Promise<ProductSectionDto> {
    const { name, position } = updateProductSectionDto;

    try {
      // Check if the section exists
      const existingSection = await this.prisma.productSection.findUnique({
        where: { id },
      });

      if (!existingSection) {
        throw new NotFoundException(`Product section with ID ${id} not found`);
      }

      // If position is being updated, check if it's already taken
      if (position && position !== existingSection.position) {
        const positionTaken = await this.prisma.productSection.findUnique({
          where: { position },
        });

        if (positionTaken) {
          throw new ConflictException(`Position ${position} is already taken`);
        }
      }

      // Update the section
      await this.prisma.productSection.update({
        where: { id },
        data: {
          ...(name && { name }),
          ...(position && { position }),
        },
      });

      // Return the updated section
      return this.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      if (error.code === 'P2002') {
        throw new ConflictException(`Position ${position} is already taken`);
      }
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    // Check if the section exists
    const section = await this.prisma.productSection.findUnique({
      where: { id },
    });

    if (!section) {
      throw new NotFoundException(`Product section with ID ${id} not found`);
    }

    // Delete all items in the section
    await this.prisma.productSectionItem.deleteMany({
      where: { productSectionId: id },
    });

    // Delete the section
    await this.prisma.productSection.delete({
      where: { id },
    });
  }

  async addProductToSection(sectionId: number, addProductDto: AddProductToSectionDto): Promise<ProductSectionDto> {
    const { productId, position } = addProductDto;

    try {
      // Check if the section exists
      const section = await this.prisma.productSection.findUnique({
        where: { id: sectionId },
      });

      if (!section) {
        throw new NotFoundException(`Product section with ID ${sectionId} not found`);
      }

      // Check if the product exists
      const product = await this.prisma.product.findUnique({
        where: { id: productId },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${productId} not found`);
      }

      // Check if the position is already taken in this section
      const existingItem = await this.prisma.productSectionItem.findFirst({
        where: {
          productSectionId: sectionId,
          position,
        },
      });

      if (existingItem) {
        throw new ConflictException(`Position ${position} is already taken in this section`);
      }

      // Add the product to the section
      await this.prisma.productSectionItem.create({
        data: {
          productId,
          productSectionId: sectionId,
          position,
        },
      });

      // Return the updated section
      return this.findOne(sectionId);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      if (error.code === 'P2002') {
        throw new ConflictException(`Position ${position} is already taken in this section`);
      }
      throw error;
    }
  }

  async removeProductFromSection(sectionId: number, itemId: number): Promise<ProductSectionDto> {
    // Check if the section exists
    const section = await this.prisma.productSection.findUnique({
      where: { id: sectionId },
    });

    if (!section) {
      throw new NotFoundException(`Product section with ID ${sectionId} not found`);
    }

    // Check if the item exists in this section
    const item = await this.prisma.productSectionItem.findFirst({
      where: {
        id: itemId,
        productSectionId: sectionId,
      },
    });

    if (!item) {
      throw new NotFoundException(`Item with ID ${itemId} not found in section ${sectionId}`);
    }

    // Remove the item
    await this.prisma.productSectionItem.delete({
      where: { id: itemId },
    });

    // Return the updated section
    return this.findOne(sectionId);
  }

  private mapToProductSectionDto(section: any): ProductSectionDto {
    return {
      id: section.id,
      name: section.name,
      position: section.position,
      createdAt: section.createdAt,
      updatedAt: section.updatedAt,
      items: section.items.map(item => ({
        id: item.id,
        productId: item.productId,
        position: item.position,
        product: {
          id: item.product.id,
          name: item.product.name,
          sku: item.product.sku,
          createdAt: item.product.createdAt,
          price: typeof item.product.price === 'object' && item.product.price !== null && 'toNumber' in item.product.price
            ? item.product.price.toNumber()
            : Number(item.product.price),
          salePrice: item.product.salePrice
            ? typeof item.product.salePrice === 'object' && item.product.salePrice !== null && 'toNumber' in item.product.salePrice
              ? item.product.salePrice.toNumber()
              : Number(item.product.salePrice)
            : null,
          stockStatus: item.product.stockStatus,
          imageUrl: item.product.images.length > 0 ? item.product.images[0].url : null,
        },
      })),
    };
  }
}
