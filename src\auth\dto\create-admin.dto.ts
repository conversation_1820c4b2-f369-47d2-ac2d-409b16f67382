import { Is<PERSON><PERSON>, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString, IsO<PERSON>al, IsBoolean } from 'class-validator';

export class CreateAdminDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsBoolean()
  isSuperAdmin?: boolean;
}
