import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { StoreModule } from './store/store.module';
import { ShopModule } from './shop/shop.module';
import { ProductSectionsModule } from './product-sections/product-sections.module';
import { AuthModule } from './auth/auth.module';
import { TaxModule } from './tax/tax.module';

@Module({
  imports: [PrismaModule, StoreModule, ShopModule, ProductSectionsModule, AuthModule, TaxModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
