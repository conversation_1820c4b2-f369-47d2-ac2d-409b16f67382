-- DropIndex
DROP INDEX IF EXISTS "product_name_trgm_idx";

-- DropIndex
DROP INDEX IF EXISTS "product_search_vector_idx";

-- Check if search_vector column exists and handle it properly
DO $$
BEGIN
    -- Check if the column exists and is a generated column
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Product'
        AND column_name = 'search_vector'
        AND is_generated = 'ALWAYS'
    ) THEN
        -- Column exists as generated, recreate indexes
        CREATE INDEX IF NOT EXISTS "product_search_vector_idx"
          ON "Product"
          USING GIN(search_vector);

        CREATE INDEX IF NOT EXISTS "product_name_trgm_idx"
          ON "Product"
          USING GIN (name gin_trgm_ops);
    ELSE
        -- Column doesn't exist or isn't generated, create it
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'Product'
            AND column_name = 'search_vector'
        ) THEN
            ALTER TABLE "Product"
            ADD COLUMN search_vector tsvector GENERATED ALWAYS AS (
                setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
                setweight(to_tsvector('english', coalesce("shortDescription", '')), 'B') ||
                setweight(to_tsvector('english', coalesce(description, '')), 'C')
            ) STORED;
        END IF;

        -- Create indexes
        CREATE INDEX IF NOT EXISTS "product_search_vector_idx"
          ON "Product"
          USING GIN(search_vector);

        CREATE INDEX IF NOT EXISTS "product_name_trgm_idx"
          ON "Product"
          USING GIN (name gin_trgm_ops);
    END IF;
END
$$;
