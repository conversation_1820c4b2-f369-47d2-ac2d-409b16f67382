-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Check if search_vector column exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Product'
        AND column_name = 'search_vector'
    ) THEN
        -- Add search_vector column to Product table
        ALTER TABLE "Product"
        ADD COLUMN search_vector tsvector GENERATED ALWAYS AS (
            setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
            setweight(to_tsvector('english', coalesce("shortDescription", '')), 'B') ||
            setweight(to_tsvector('english', coalesce(description, '')), 'C')
        ) STORED;
    END IF;
END
$$;

-- Drop indexes if they exist and recreate them
DROP INDEX IF EXISTS "product_search_vector_idx";
DROP INDEX IF EXISTS "product_name_trgm_idx";

-- Create GIN index for search_vector
CREATE INDEX "product_search_vector_idx"
  ON "Product"
  USING GIN(search_vector);

-- Create trigram index on name for fuzzy search
CREATE INDEX "product_name_trgm_idx"
  ON "Product"
  USING GIN (name gin_trgm_ops);
