import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { CreateMainCategoryDto } from './dto/create-main-category.dto';
import { UpdateMainCategoryDto } from './dto/update-main-category.dto';
import { AddCategoriesToMainCategoryDto } from './dto/add-categories-to-main-category.dto';
import { Category, MainCategory, Product } from '@prisma/client';

@Injectable()
export class StoreService {
  constructor(private prisma: PrismaService) {}

  // Category Management
  async createCategory(createCategoryDto: CreateCategoryDto): Promise<Category> {
    return this.prisma.category.create({
      data: createCategoryDto,
    });
  }

  async getAllCategories(): Promise<Category[]> {
    return this.prisma.category.findMany();
  }

  async getCategoryById(id: number): Promise<Category> {
    const category = await this.prisma.category.findUnique({
      where: { id },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    return category;
  }

  async updateCategory(id: number, updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    try {
      return await this.prisma.category.update({
        where: { id },
        data: updateCategoryDto,
      });
    } catch (error) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
  }

  async deleteCategory(id: number): Promise<Category> {
    try {
      // First delete all relations in ProductCategories table
      await this.prisma.productCategories.deleteMany({
        where: {
          categoryId: id,
        },
      });

      // Then delete the category
      return await this.prisma.category.delete({
        where: { id },
      });
    } catch (error) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
  }

  // MainCategory Management
  async createMainCategory(createMainCategoryDto: CreateMainCategoryDto) {
    const { categoryIds = [], ...mainCategoryData } = createMainCategoryDto;

    try {
      // Create the main category
      const mainCategory = await this.prisma.mainCategory.create({
        data: {
          ...mainCategoryData,
          ...(categoryIds.length > 0
            ? {
                categories: {
                  connect: categoryIds.map(id => ({ id })),
                },
              }
            : {}),
        },
        include: {
          categories: true,
        },
      });

      return mainCategory;
    } catch (error) {
      console.error('Error creating main category:', error);
      throw error;
    }
  }

  async getAllMainCategories() {
    return this.prisma.mainCategory.findMany({
      include: {
        categories: true,
      },
    });
  }

  async getMainCategoryById(id: number) {
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { id },
      include: {
        categories: true,
      },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with ID ${id} not found`);
    }

    return mainCategory;
  }

  async updateMainCategory(id: number, updateMainCategoryDto: UpdateMainCategoryDto) {
    const { categoryIds, ...mainCategoryData } = updateMainCategoryDto;

    try {
      // Update the main category
      const mainCategory = await this.prisma.mainCategory.update({
        where: { id },
        data: {
          ...mainCategoryData,
          ...(categoryIds
            ? {
                categories: {
                  set: categoryIds.map(id => ({ id })),
                },
              }
            : {}),
        },
        include: {
          categories: true,
        },
      });

      return mainCategory;
    } catch (error) {
      throw new NotFoundException(`Main category with ID ${id} not found`);
    }
  }

  async addCategoriesToMainCategory(id: number, addCategoriesDto: AddCategoriesToMainCategoryDto) {
    const { categoryIds } = addCategoriesDto;

    try {
      // Check if the main category exists
      const mainCategory = await this.prisma.mainCategory.findUnique({
        where: { id },
      });

      if (!mainCategory) {
        throw new NotFoundException(`Main category with ID ${id} not found`);
      }

      // Update categories for the main category
      await this.prisma.mainCategory.update({
        where: { id },
        data: {
          categories: {
            connect: categoryIds.map(categoryId => ({ id: categoryId })),
          },
        },
      });

      // Return the updated main category with categories
      return this.prisma.mainCategory.findUnique({
        where: { id },
        include: {
          categories: true,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error adding categories to main category:', error);
      throw error;
    }
  }

  async deleteMainCategory(id: number) {
    try {
      // First update all categories to remove the mainCategoryId
      await this.prisma.category.updateMany({
        where: {
          mainCategoryId: id,
        },
        data: {
          mainCategoryId: null,
        },
      });

      // Then delete the main category
      return await this.prisma.mainCategory.delete({
        where: { id },
        include: {
          categories: true,
        },
      });
    } catch (error) {
      throw new NotFoundException(`Main category with ID ${id} not found`);
    }
  }

  // Product Management
  async createProduct(createProductDto: CreateProductDto): Promise<Product> {
    const {
      categoryIds = [],
      images = [],
      tags = [],
      listings = [],
      ...productData
    } = createProductDto;

    try {
      // 1) Create the product with images
      const product = await this.prisma.product.create({
        data: {
          ...productData,
          // Generate a slug from the name to ensure uniqueness
          slug: productData.name.toLowerCase().replace(/\s+/g, '-'),
          ...(images.length > 0
            ? {
                images: {
                  create: images,
                },
              }
            : {}),
          ...(categoryIds.length > 0
            ? {
                categories: {
                  connect: categoryIds.map(id => ({ id })),
                },
              }
            : {}),
          ...(tags.length > 0
            ? {
                tags: {
                  connectOrCreate: tags.map(tag => ({
                    where: { name: tag },
                    create: {
                      name: tag,
                      slug: tag.toLowerCase().replace(/\s+/g, '-')
                    }
                  }))
                },
              }
            : {}),
        },
      });

      // 2) Create category relationships with skipDuplicates
      if (categoryIds.length > 0) {
        await this.prisma.productCategories.createMany({
          data: categoryIds.map((categoryId) => ({
            productId: product.id,
            categoryId,
          })),
          skipDuplicates: true,
        });
      }

      // 3) Create tag relationships if needed
      if (tags.length > 0) {
        // Create ProductTags entries
        for (const tagName of tags) {
          // Find or create the tag
          const tag = await this.prisma.tag.upsert({
            where: { name: tagName },
            update: {},
            create: {
              name: tagName,
              slug: tagName.toLowerCase().replace(/\s+/g, '-')
            },
          });

          // Create the relationship
          await this.prisma.productTags.create({
            data: {
              productId: product.id,
              tagId: tag.id,
            },
          });
        }
      }

      // 4) Create listings if provided
      if (listings.length > 0) {
        await this.prisma.listing.createMany({
          data: listings.map(listing => ({
            title: listing.title,
            content: listing.content,
            productId: product.id
          })),
        });
      }

      // 5) Return the complete product with relationships
      return await this.prisma.product.findUnique({
        where: { id: product.id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
        },
      });
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  async getAllProducts() {
    return this.prisma.product.findMany({
      select: {
        id: true,
        name: true,
        shortDescription: true,
        price: true,
        sku: true,
        stockQuantity: true,
        stockStatus: true,
        type: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
      },
    });
  }

  async getProductById(id: number): Promise<Product> {
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
        ProductAttribute: {
          include: {
            attribute: true,
            values: true,
          },
        },
        variants: {
          include: {
            ProductImage: true,
            attributes: {
              include: {
                value: true,
              },
            },
          },
        },
      },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    return product;
  }

  async getProductsByCategory(categoryId: number) {
    const category = await this.prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${categoryId} not found`);
    }

    return this.prisma.product.findMany({
      where: {
        ProductCategories: {
          some: {
            categoryId: categoryId,
          },
        },
      },
      select: {
        id: true,
        name: true,
        shortDescription: true,
        price: true,
        sku: true,
        stockQuantity: true,
        stockStatus: true,
        type: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
        // Include product attributes and variants for GROUPED products
        ProductAttribute: {
          select: {
            id: true,
            attribute: {
              select: {
                id: true,
                name: true,
              },
            },
            values: {
              select: {
                id: true,
                value: true,
              },
            },
          },
        },
        variants: {
          select: {
            id: true,
            sku: true,
            price: true,
            stockQuantity: true,
            stockStatus: true,
            ProductImage: {
              take: 1,
              select: {
                url: true,
              },
            },
            attributes: {
              select: {
                value: {
                  select: {
                    id: true,
                    value: true,
                  },
                },
              },
            },
          },
        },
      },
    });
  }

  async updateProduct(id: number, updateProductDto: UpdateProductDto): Promise<Product> {
    // Pull out everything we don't want in `data`
    const {
      categoryIds = [],
      images: rawImages = [],
      tags = [],
      listings = [],
      deleteListingIds = [],
      categories,            // ignore if present
      id: _ignoreId,
      createdAt,
      updatedAt,
      productAttributes,      // extract these fields
      variants,              // extract these fields
      ...allowedFields       // now only { sku?, name?, description?, … }
    } = updateProductDto;

    // 0) Ensure it exists
    const existing = await this.prisma.product.findUnique({ where: { id } });
    if (!existing) throw new NotFoundException(`Product with ID ${id} not found`);

    // 1) Prepare a "clean" images array
    const images = rawImages.map(({ url, position }) => ({ url, position }));

    // 2) Update scalars + replace images
    await this.prisma.product.update({
      where: { id },
      data: {
        ...allowedFields,
        ...(images.length > 0
          ? {
              images: {
                deleteMany: {},   // remove old
                create: images,   // add new
              },
            }
          : {}),
        ...(categoryIds.length > 0
          ? {
              categories: {
                set: categoryIds.map(id => ({ id })),
              },
            }
          : {}),
      },
    });

    // 3) Rebuild category links in explicit join table
    await this.prisma.productCategories.deleteMany({ where: { productId: id } });
    if (categoryIds.length > 0) {
      await this.prisma.productCategories.createMany({
        data: categoryIds.map((categoryId) => ({ productId: id, categoryId })),
        skipDuplicates: true,
      });
    }

    // 4) Handle tags if provided
    if (tags.length > 0) {
      // Delete existing tag relationships
      await this.prisma.productTags.deleteMany({ where: { productId: id } });

      // Create new tag relationships
      for (const tagName of tags) {
        // Find or create the tag
        const tag = await this.prisma.tag.upsert({
          where: { name: tagName },
          update: {},
          create: {
            name: tagName,
            slug: tagName.toLowerCase().replace(/\s+/g, '-')
          },
        });

        // Create the relationship
        await this.prisma.productTags.create({
          data: {
            productId: id,
            tagId: tag.id,
          },
        });
      }
    }

    // Handle listings
    // Delete listings if specified
    if (deleteListingIds.length > 0) {
      await this.prisma.listing.deleteMany({
        where: {
          id: { in: deleteListingIds },
          productId: id,
        },
      });
    }

    // Add new listings if provided
    if (listings && listings.length > 0) {
      await this.prisma.listing.createMany({
        data: listings.map(listing => ({
          title: listing.title,
          content: listing.content,
          productId: id
        })),
        skipDuplicates: true,
      });
    }

    // 5) Return the updated product with all relationships
    return await this.prisma.product.findUnique({
      where: { id },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
      },
    });
  }






  async deleteProduct(id: number): Promise<Product> {
    try {
      // First check if the product exists
      const product = await this.prisma.product.findUnique({
        where: { id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
        },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      // Delete all relations in ProductCategories table
      await this.prisma.productCategories.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all relations in ProductTags table
      await this.prisma.productTags.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all product images
      await this.prisma.productImage.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all listings associated with the product
      await this.prisma.listing.deleteMany({
        where: {
          productId: id,
        },
      });

      // Finally delete the product
      return await this.prisma.product.delete({
        where: { id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error deleting product:', error);
      throw error;
    }
  }
}



























