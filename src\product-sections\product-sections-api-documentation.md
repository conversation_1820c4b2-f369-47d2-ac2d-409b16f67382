# Product Sections API Documentation

This document provides examples of how to use the Product Sections API endpoints correctly.

## Base URL

All API endpoints are prefixed with:
```
http://localhost:3300/api/store/product-sections
```

## Product Sections Management

### Create a New Product Section

Creates a new product section with an optional list of products.

**Endpoint:**
```
POST /api/store/product-sections
```

**Request Body:**
```json
{
  "name": "Featured Products",
  "position": 1,
  "productIds": [1, 2, 3]
}
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/store/product-sections \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Featured Products",
    "position": 1,
    "productIds": [1, 2, 3]
  }'
```

**Example Response:**
```json
{
  "id": 1,
  "name": "Featured Products",
  "position": 1,
  "createdAt": "2025-05-10T13:45:00.000Z",
  "updatedAt": "2025-05-10T13:45:00.000Z",
  "items": [
    {
      "id": 1,
      "productId": 1,
      "position": 1,
      "product": {
        "id": 1,
        "name": "Hair brush",
        "sku": "hair-brush-1",
        "price": 15,
        "salePrice": 12,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
      }
    },
    {
      "id": 2,
      "productId": 2,
      "position": 2,
      "product": {
        "id": 2,
        "name": "Shampoo",
        "sku": "shampoo-1",
        "price": 25.99,
        "salePrice": null,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://example.com/shampoo.jpg"
      }
    },
    {
      "id": 3,
      "productId": 3,
      "position": 3,
      "product": {
        "id": 3,
        "name": "Conditioner",
        "sku": "conditioner-1",
        "price": 22.99,
        "salePrice": null,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://example.com/conditioner.jpg"
      }
    }
  ]
}
```

### Get All Product Sections

Retrieves all product sections with their products.

**Endpoint:**
```
GET /api/store/product-sections
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/product-sections
```

**Example Response:**
```json
[
  {
    "id": 1,
    "name": "Featured Products",
    "position": 1,
    "createdAt": "2025-05-10T13:45:00.000Z",
    "updatedAt": "2025-05-10T13:45:00.000Z",
    "items": [
      {
        "id": 1,
        "productId": 1,
        "position": 1,
        "product": {
          "id": 1,
          "name": "Hair brush",
          "sku": "hair-brush-1",
          "price": 15,
          "salePrice": 12,
          "stockStatus": "IN_STOCK",
          "imageUrl": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
        }
      },
      {
        "id": 2,
        "productId": 2,
        "position": 2,
        "product": {
          "id": 2,
          "name": "Shampoo",
          "sku": "shampoo-1",
          "price": 25.99,
          "salePrice": null,
          "stockStatus": "IN_STOCK",
          "imageUrl": "https://example.com/shampoo.jpg"
        }
      }
    ]
  },
  {
    "id": 2,
    "name": "New Arrivals",
    "position": 2,
    "createdAt": "2025-05-10T13:50:00.000Z",
    "updatedAt": "2025-05-10T13:50:00.000Z",
    "items": [
      {
        "id": 3,
        "productId": 3,
        "position": 1,
        "product": {
          "id": 3,
          "name": "Conditioner",
          "sku": "conditioner-1",
          "price": 22.99,
          "salePrice": null,
          "stockStatus": "IN_STOCK",
          "imageUrl": "https://example.com/conditioner.jpg"
        }
      }
    ]
  }
]
```

### Get Product Section by ID

Retrieves a specific product section by its ID.

**Endpoint:**
```
GET /api/store/product-sections/:id
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/product-sections/1
```

**Example Response:**
```json
{
  "id": 1,
  "name": "Featured Products",
  "position": 1,
  "createdAt": "2025-05-10T13:45:00.000Z",
  "updatedAt": "2025-05-10T13:45:00.000Z",
  "items": [
    {
      "id": 1,
      "productId": 1,
      "position": 1,
      "product": {
        "id": 1,
        "name": "Hair brush",
        "sku": "hair-brush-1",
        "price": 15,
        "salePrice": 12,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
      }
    },
    {
      "id": 2,
      "productId": 2,
      "position": 2,
      "product": {
        "id": 2,
        "name": "Shampoo",
        "sku": "shampoo-1",
        "price": 25.99,
        "salePrice": null,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://example.com/shampoo.jpg"
      }
    }
  ]
}
```

### Get Product Section by Position

Retrieves a specific product section by its position.

**Endpoint:**
```
GET /api/store/product-sections/position/:position
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/product-sections/position/1
```

**Example Response:**
```json
{
  "id": 1,
  "name": "Featured Products",
  "position": 1,
  "createdAt": "2025-05-10T13:45:00.000Z",
  "updatedAt": "2025-05-10T13:45:00.000Z",
  "items": [
    {
      "id": 1,
      "productId": 1,
      "position": 1,
      "product": {
        "id": 1,
        "name": "Hair brush",
        "sku": "hair-brush-1",
        "price": 15,
        "salePrice": 12,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
      }
    },
    {
      "id": 2,
      "productId": 2,
      "position": 2,
      "product": {
        "id": 2,
        "name": "Shampoo",
        "sku": "shampoo-1",
        "price": 25.99,
        "salePrice": null,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://example.com/shampoo.jpg"
      }
    }
  ]
}
```

### Update Product Section

Updates a product section's name or position.

**Endpoint:**
```
PATCH /api/store/product-sections/:id
```

**Request Body:**
```json
{
  "name": "Top Featured Products",
  "position": 3
}
```

**Example Request:**
```bash
curl -X PATCH http://localhost:3300/api/store/product-sections/1 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Top Featured Products",
    "position": 3
  }'
```

**Example Response:**
```json
{
  "id": 1,
  "name": "Top Featured Products",
  "position": 3,
  "createdAt": "2025-05-10T13:45:00.000Z",
  "updatedAt": "2025-05-10T14:00:00.000Z",
  "items": [
    {
      "id": 1,
      "productId": 1,
      "position": 1,
      "product": {
        "id": 1,
        "name": "Hair brush",
        "sku": "hair-brush-1",
        "price": 15,
        "salePrice": 12,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
      }
    },
    {
      "id": 2,
      "productId": 2,
      "position": 2,
      "product": {
        "id": 2,
        "name": "Shampoo",
        "sku": "shampoo-1",
        "price": 25.99,
        "salePrice": null,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://example.com/shampoo.jpg"
      }
    }
  ]
}
```

### Delete Product Section

Deletes a product section and all its items.

**Endpoint:**
```
DELETE /api/store/product-sections/:id
```

**Example Request:**
```bash
curl -X DELETE http://localhost:3300/api/store/product-sections/1
```

**Example Response:**
```
204 No Content
```

### Add Product to Section

Adds a product to a section at a specific position.

**Endpoint:**
```
POST /api/store/product-sections/:id/products
```

**Request Body:**
```json
{
  "productId": 4,
  "position": 3
}
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/store/product-sections/1/products \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 4,
    "position": 3
  }'
```

**Example Response:**
```json
{
  "id": 1,
  "name": "Featured Products",
  "position": 1,
  "createdAt": "2025-05-10T13:45:00.000Z",
  "updatedAt": "2025-05-10T14:15:00.000Z",
  "items": [
    {
      "id": 1,
      "productId": 1,
      "position": 1,
      "product": {
        "id": 1,
        "name": "Hair brush",
        "sku": "hair-brush-1",
        "price": 15,
        "salePrice": 12,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
      }
    },
    {
      "id": 2,
      "productId": 2,
      "position": 2,
      "product": {
        "id": 2,
        "name": "Shampoo",
        "sku": "shampoo-1",
        "price": 25.99,
        "salePrice": null,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://example.com/shampoo.jpg"
      }
    },
    {
      "id": 4,
      "productId": 4,
      "position": 3,
      "product": {
        "id": 4,
        "name": "Hair Mask",
        "sku": "hair-mask-1",
        "price": 18.99,
        "salePrice": null,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://example.com/hair-mask.jpg"
      }
    }
  ]
}
```

### Remove Product from Section

Removes a product from a section.

**Endpoint:**
```
DELETE /api/store/product-sections/:sectionId/products/:itemId
```

**Example Request:**
```bash
curl -X DELETE http://localhost:3300/api/store/product-sections/1/products/2
```

**Example Response:**
```json
{
  "id": 1,
  "name": "Featured Products",
  "position": 1,
  "createdAt": "2025-05-10T13:45:00.000Z",
  "updatedAt": "2025-05-10T14:30:00.000Z",
  "items": [
    {
      "id": 1,
      "productId": 1,
      "position": 1,
      "product": {
        "id": 1,
        "name": "Hair brush",
        "sku": "hair-brush-1",
        "price": 15,
        "salePrice": 12,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
      }
    },
    {
      "id": 4,
      "productId": 4,
      "position": 3,
      "product": {
        "id": 4,
        "name": "Hair Mask",
        "sku": "hair-mask-1",
        "price": 18.99,
        "salePrice": null,
        "stockStatus": "IN_STOCK",
        "imageUrl": "https://example.com/hair-mask.jpg"
      }
    }
  ]
}
```
