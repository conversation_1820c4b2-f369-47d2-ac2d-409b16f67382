-- Optimize search configuration for better fuzzy matching

-- Set trigram similarity threshold for better fuzzy matching
-- This allows "citronela" to match "citronella" with a lower threshold
SELECT set_limit(0.15);

-- Ensure pg_trgm extension is enabled
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Ensure unaccent extension is enabled for better text normalization
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Create additional indexes for better search performance if they don't exist
CREATE INDEX IF NOT EXISTS "product_description_trgm_idx"
  ON "Product"
  USING GIN (description gin_trgm_ops)
  WHERE description IS NOT NULL;

CREATE INDEX IF NOT EXISTS "product_short_description_trgm_idx"
  ON "Product"
  USING GIN ("shortDescription" gin_trgm_ops)
  WHERE "shortDescription" IS NOT NULL;

-- Create a simple immutable function for text normalization
CREATE OR REPLACE FUNCTION normalize_text(input_text TEXT)
RETURNS TEXT AS $$
  SELECT lower(unaccent(trim(COALESCE(input_text, ''))));
$$ LANGUAGE SQL IMMUTABLE STRICT;
