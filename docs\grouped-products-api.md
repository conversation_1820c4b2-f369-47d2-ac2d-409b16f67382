# Grouped Products API Documentation

This document provides examples and usage instructions for the Grouped Products API endpoints.

## Update Grouped Product Base

This endpoint allows you to update a grouped product's base information without modifying its variants.

**Endpoint:** `PUT /store/grouped-products/:id/base`

### Example 1: Update Basic Information

```json
{
  "name": "Updated Product Name",
  "description": "Updated product description",
  "shortDescription": "Updated short description",
  "price": 99.99,
  "sku": "UPD-SKU-123",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK"
}
```

### Example 2: Update Categories and Tags

```json
{
  "categoryIds": [1, 2, 3],
  "tags": ["new-tag", "featured", "summer-collection"]
}
```

### Example 3: Update Images

```json
{
  "images": [
    {
      "id": 1,
      "url": "https://example.com/updated-image.jpg",
      "position": 1
    },
    {
      "url": "https://example.com/new-image.jpg",
      "position": 2
    },
    {
      "id": 3,
      "delete": true
    }
  ]
}
```

### Example 4: Update Listings

```json
{
  "listings": [
    {
      "id": 1,
      "title": "Updated Listing Title",
      "content": "Updated listing content"
    },
    {
      "title": "New Listing",
      "content": "New listing content"
    },
    {
      "id": 3,
      "delete": true
    }
  ]
}
```

### Example 5: Combined Update

```json
{
  "name": "Updated Product Name",
  "description": "Updated product description",
  "categoryIds": [1, 2],
  "tags": ["new-tag"],
  "images": [
    {
      "url": "https://example.com/new-image.jpg",
      "position": 1
    }
  ],
  "listings": [
    {
      "title": "New Listing",
      "content": "New listing content"
    }
  ]
}
```

## Update Grouped Product Variant

This endpoint allows you to update a specific variant of a grouped product.

**Endpoint:** `PUT /store/grouped-products/:productId/variants/:variantId`

### Example 1: Update Basic Variant Information

```json
{
  "sku": "VAR-SKU-123",
  "price": 49.99,
  "salePrice": 39.99,
  "saleStart": "2024-03-20T00:00:00Z",
  "saleEnd": "2024-04-20T00:00:00Z",
  "stockQuantity": 50,
  "stockStatus": "IN_STOCK"
}
```

### Example 2: Update Variant Images

```json
{
  "images": [
    {
      "id": 1,
      "url": "https://example.com/updated-variant-image.jpg",
      "position": 1
    },
    {
      "url": "https://example.com/new-variant-image.jpg",
      "position": 2
    },
    {
      "id": 3,
      "delete": true
    }
  ]
}
```

### Example 3: Update Variant Attributes

```json
{
  "attributeValueIds": [1, 2, 3]
}
```

### Example 4: Combined Variant Update

```json
{
  "sku": "VAR-SKU-123",
  "price": 49.99,
  "salePrice": 39.99,
  "stockQuantity": 50,
  "stockStatus": "IN_STOCK",
  "images": [
    {
      "url": "https://example.com/new-variant-image.jpg",
      "position": 1
    }
  ],
  "attributeValueIds": [1, 2, 3]
}
```

## Response Format

Both endpoints return the complete product object with all its relationships:

```json
{
  "id": 1,
  "name": "Product Name",
  "description": "Product description",
  "shortDescription": "Short description",
  "price": 99.99,
  "sku": "SKU-123",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "type": "GROUPED",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/image.jpg",
      "position": 1
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Category 1"
    }
  ],
  "tags": [
    {
      "id": 1,
      "name": "tag1"
    }
  ],
  "listings": [
    {
      "id": 1,
      "title": "Listing Title",
      "content": "Listing content"
    }
  ],
  "ProductAttribute": [
    {
      "id": 1,
      "attribute": {
        "id": 1,
        "name": "Color"
      },
      "values": [
        {
          "id": 1,
          "value": "Red"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "sku": "VAR-SKU-123",
      "price": 49.99,
      "salePrice": 39.99,
      "saleStart": "2024-03-20T00:00:00Z",
      "saleEnd": "2024-04-20T00:00:00Z",
      "stockQuantity": 50,
      "stockStatus": "IN_STOCK",
      "ProductImage": [
        {
          "id": 1,
          "url": "https://example.com/variant-image.jpg",
          "position": 1
        }
      ],
      "attributes": [
        {
          "value": {
            "id": 1,
            "value": "Red"
          }
        }
      ]
    }
  ]
}
```

## Error Responses

Both endpoints may return the following error responses:

- `404 Not Found`: When the product or variant is not found
- `400 Bad Request`: When the product is not a grouped product or when invalid data is provided
- `500 Internal Server Error`: When an unexpected error occurs

Example error response:

```json
{
  "statusCode": 404,
  "message": "Product with ID 1 not found",
  "error": "Not Found"
}
``` 