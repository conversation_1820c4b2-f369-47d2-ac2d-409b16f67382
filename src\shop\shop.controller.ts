import { Controller, Get, Param, ParseIntPipe, Query, NotFoundException } from '@nestjs/common';
import { ShopService } from './shop.service';
import { MainCategoryDto } from './dto/main-category.dto';
import { SubcategoriesDto } from './dto/subcategories.dto';
import { PaginatedProductsDto } from './dto/paginated-products.dto';
import { ProductDetailsDto } from './dto/product-details.dto';
import { EnhancedPaginatedProductsDto } from './dto/paginated-products.dto';


@Controller('shop')
export class ShopController {
  constructor(private readonly shopService: ShopService) {}

  @Get('main-categories')
  getMainCategories(): Promise<{ data: MainCategoryDto[] }> {
    return this.shopService.getMainCategories();
  }

  @Get('main-categories/:mainId/categories')
  getSubcategories(
    @Param('mainId', ParseIntPipe) mainId: number,
  ): Promise<SubcategoriesDto> {
    return this.shopService.getSubcategories(mainId);
  }

  @Get('categories/:catId/products')
  getProductsByCategory(
    @Param('catId', ParseIntPipe) catId: number,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 20,
  ): Promise<PaginatedProductsDto> {
    return this.shopService.getProductsByCategory(catId, page, limit);
  }

  
  @Get('products')
  getAllProducts(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<PaginatedProductsDto> {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 20;
    return this.shopService.getAllProducts(pageNumber, limitNumber);
  }

  @Get('products/names')
  getAllProductNames(): Promise<{ data: { id: number; name: string; slug: string }[] }> {
    return this.shopService.getAllProductNames();
  }

  @Get('products/slug/:slug')
  getProductDetailsBySlug(
    @Param('slug') slug: string,
    @Query('password') password?: string,
  ): Promise<ProductDetailsDto> {
    return this.shopService.getProductDetailsBySlug(slug, password);
  }

  // @Get('products/:id')
  // getProductDetails(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Query('password') password?: string,
  // ): Promise<ProductDetailsDto> {
  //   return this.shopService.getProductDetails(id, password);
  // }

  @Get('products/search')
  async search(
    @Query('query') query: string,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 20,
  ) {
    if (!query?.trim()) return [];
    return this.shopService.searchProducts(query.trim(), limit, page);
  }

  @Get(':mainCategorySlug/:categorySlug')
  getProductsByMainCategoryAndCategorySlug(
    @Param('mainCategorySlug') mainCategorySlug: string,
    @Param('categorySlug') categorySlug: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<EnhancedPaginatedProductsDto> {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 20;
    return this.shopService.getProductsByMainCategoryAndCategorySlug(mainCategorySlug, categorySlug, pageNumber, limitNumber);
  }

  @Get(':mainCategorySlug')
  getProductsByMainCategorySlug(
    @Param('mainCategorySlug') mainCategorySlug: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<EnhancedPaginatedProductsDto> {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 20;
    return this.shopService.getProductsByMainCategorySlug(mainCategorySlug, pageNumber, limitNumber);
  }

}