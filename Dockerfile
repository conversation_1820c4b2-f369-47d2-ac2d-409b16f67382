# Stage 1: build
FROM node:18-alpine AS builder
WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install

# Copy source code
COPY . .

# Generate Prisma client first
RUN npx prisma generate

# Then build the application
RUN npm run build

# Stage 2: runtime
FROM node:18-alpine AS runner
WORKDIR /app

# Install production dependencies
COPY package*.json ./
RUN npm install --only=production

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Copy Prisma schema, migrations, and generated client
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma

# Create a directory for Prisma migrations
RUN mkdir -p prisma/migrations

# Add a script to run migrations and start the app
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma

# Copy entrypoint script
COPY docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh
# Make sure the script has Unix line endings
RUN sed -i 's/\r$//' docker-entrypoint.sh

# Expose the port your NestJS app listens on
EXPOSE 3000

# Use a non-root user for security
USER node

# Set entrypoint to our script
ENTRYPOINT ["./docker-entrypoint.sh"]

# Start command
CMD ["node", "dist/main"]
