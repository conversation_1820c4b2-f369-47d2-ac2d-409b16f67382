import { IsNotEmpty, IsString, IsOptional, IsArray, IsDecimal, IsInt, IsEnum, IsPositive, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { StockStatus } from '@prisma/client';

export class CreateProductVariantDto {
  @IsNotEmpty()
  @IsString()
  sku: string;

  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '2' })
  price: string;

  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  salePrice?: string;

  @IsOptional()
  @IsInt()
  @IsPositive()
  stockQuantity?: number;

  @IsOptional()
  @IsEnum(StockStatus)
  stockStatus?: StockStatus;

  @IsNotEmpty()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  attributeValueIds: number[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductVariantImageDto)
  images?: ProductVariantImageDto[];
}

export class ProductVariantImageDto {
  @IsNotEmpty()
  @IsString()
  url: string;

  @IsOptional()
  @IsInt()
  position?: number;
}
