import { IsOptional, IsString, IsArray, IsEnum, IsInt, IsPositive, IsDateString, IsDecimal, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { AccessLevel, ProductType, StockStatus, TaxClass, TaxStatus } from '@prisma/client';
import { CreateProductAttributeDto } from './create-product-attribute.dto';
import { CreateProductVariantDto } from './create-product-variant.dto';
import { CreateListingDto } from './create-listing.dto';

export class UpdateProductDto {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsOptional()
  @IsDateString()
  createdAt?: Date;

  @IsOptional()
  @IsDateString()
  updatedAt?: Date;

  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  shortDescription?: string;

  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  price?: string; // Using string for Decimal

  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  salePrice?: string; // Using string for Decimal

  @IsOptional()
  @IsDateString()
  saleStart?: string;

  @IsOptional()
  @IsDateString()
  saleEnd?: string;

  @IsOptional()
  @IsInt()
  @IsPositive()
  stockQuantity?: number;

  @IsOptional()
  @IsEnum(StockStatus)
  stockStatus?: StockStatus;

  @IsOptional()
  @IsEnum(TaxStatus)
  taxStatus?: TaxStatus;

  @IsOptional()
  @IsEnum(TaxClass)
  taxClass?: TaxClass;

  @IsOptional()
  @IsEnum(ProductType)
  type?: ProductType;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  categoryIds?: number[];

  @IsOptional()
  @IsArray()
  images?: { url: string; position?: number }[];

  @IsOptional()
  @IsArray()
  categories?: any[];

  @IsOptional()
  @IsEnum(AccessLevel)
  access?: AccessLevel;

  @IsOptional()
  @IsString()
  password?: string;

  // Fields for GROUPED product type
  @IsOptional()
  @IsArray()
  productAttributes?: CreateProductAttributeDto[];

  @IsOptional()
  @IsArray()
  variants?: CreateProductVariantDto[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];


  // // For deleting related entities
  // @IsOptional()
  // @IsArray()
  // @IsInt({ each: true })
  // @Type(() => Number)
  // deleteProductAttributeIds?: number[];

  // @IsOptional()
  // @IsArray()
  // @IsInt({ each: true })
  // @Type(() => Number)
  // deleteVariantIds?: number[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateListingDto)
  listings?: CreateListingDto[];

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  deleteListingIds?: number[];
}



