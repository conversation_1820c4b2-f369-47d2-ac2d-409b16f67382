import { Test, TestingModule } from '@nestjs/testing';
import { ShopService } from './shop.service';
import { PrismaService } from '../prisma/prisma.service';
import { NotFoundException } from '@nestjs/common';

describe('ShopService', () => {
  let service: ShopService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ShopService,
        {
          provide: PrismaService,
          useValue: {
            mainCategory: {
              findMany: jest.fn(),
              findUnique: jest.fn(),
            },
            category: {
              findMany: jest.fn(),
              findUnique: jest.fn(),
            },
            productCategories: {
              count: jest.fn(),
            },
            product: {
              findMany: jest.fn(),
              findUnique: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<ShopService>(ShopService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getMainCategories', () => {
    it('should return main categories with counts', async () => {
      const mockMainCategories = [
        {
          id: 1,
          name: 'Face Care',
          slug: 'face-care',
          imageUrl: 'https://example.com/face-care.jpg',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const mockSubcategories = [
        { id: 101, name: 'Category 1', slug: 'category-1', imageUrl: 'https://example.com/cat1.jpg', description: null, mainCategoryId: 1 },
        { id: 102, name: 'Category 2', slug: 'category-2', imageUrl: 'https://example.com/cat2.jpg', description: null, mainCategoryId: 1 }
      ];

      jest.spyOn(prismaService.mainCategory, 'findMany').mockResolvedValue(mockMainCategories);
      jest.spyOn(prismaService.category, 'findMany').mockResolvedValue(mockSubcategories);
      jest.spyOn(prismaService.productCategories, 'count').mockResolvedValue(12);

      const result = await service.getMainCategories();

      expect(result).toEqual({
        data: [
          {
            id: 1,
            name: 'Face Care',
            slug: 'face-care',
            imageUrl: 'https://example.com/face-care.jpg',
            count: 12,
          },
        ],
      });
    });
  });

  describe('getSubcategories', () => {
    it('should return subcategories for a main category', async () => {
      const mockMainCategory = {
        id: 1,
        name: 'Face Care',
        slug: 'face-care',
        imageUrl: 'https://example.com/face-care.jpg',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockSubcategories = [
        {
          id: 101,
          name: 'Cleansers',
          slug: 'face-cleansers',
          imageUrl: 'https://example.com/cleansers.jpg',
          description: null,
          mainCategoryId: 1,
        },
      ];

      jest.spyOn(prismaService.mainCategory, 'findUnique').mockResolvedValue(mockMainCategory);
      jest.spyOn(prismaService.category, 'findMany').mockResolvedValue(mockSubcategories);
      jest.spyOn(prismaService.productCategories, 'count').mockResolvedValue(4);

      const result = await service.getSubcategories(1);

      expect(result).toEqual({
        mainCategory: {
          id: 1,
          name: 'Face Care',
          slug: 'face-care',
        },
        subcategories: [
          {
            id: 101,
            name: 'Cleansers',
            slug: 'face-cleansers',
            imageUrl: 'https://example.com/cleansers.jpg',
            count: 4,
            parentId: 1,
          },
        ],
      });
    });

    it('should throw NotFoundException if main category not found', async () => {
      jest.spyOn(prismaService.mainCategory, 'findUnique').mockResolvedValue(null);

      await expect(service.getSubcategories(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getProductsByCategory', () => {
    it('should return paginated products for a category', async () => {
      const mockCategory = {
        id: 101,
        name: 'Cleansers',
        slug: 'face-cleansers',
        imageUrl: 'https://example.com/cleansers.jpg',
        description: null,
        mainCategoryId: 1,
      };

      const mockProducts = [
        {
          id: 1001,
          sku: 'FC-0001',
          name: 'Gentle Foaming Cleanser',
          price: { toNumber: () => 24.99 },
          salePrice: null,
          stockStatus: 'IN_STOCK',
          createdAt: new Date(),
          updatedAt: new Date(),
          description: 'A gentle cleanser',
          shortDescription: 'Gentle cleanser',
          stockQuantity: 100,
          type: 'SIMPLE',
          access: 'PUBLIC',
          password: null,
          saleStart: null,
          saleEnd: null,
          taxStatus: 'TAXABLE',
          taxClass: 'STANDARD',
          images: [{ url: 'https://example.com/product1.jpg' }],
        },
      ];

      jest.spyOn(prismaService.category, 'findUnique').mockResolvedValue(mockCategory);
      jest.spyOn(prismaService.productCategories, 'count').mockResolvedValue(4);
      jest.spyOn(prismaService.product, 'findMany').mockResolvedValue(mockProducts);

      const result = await service.getProductsByCategory(101, 1, 20);

      expect(result.pagination).toEqual({
        total: 4,
        page: 1,
        limit: 20,
      });
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toMatchObject({
        id: 1001,
        sku: 'FC-0001',
        name: 'Gentle Foaming Cleanser',
        slug: 'gentle-foaming-cleanser',
        imageUrl: 'https://example.com/product1.jpg',
        price: 24.99,
        salePrice: null,
        inStock: true,
      });
    });

    it('should throw NotFoundException if category not found', async () => {
      jest.spyOn(prismaService.category, 'findUnique').mockResolvedValue(null);

      await expect(service.getProductsByCategory(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getProductDetails', () => {
    it('should return product details for a public product', async () => {
      const mockProduct = {
        id: 1001,
        sku: 'FC-0001',
        name: 'Gentle Foaming Cleanser',
        price: { toNumber: () => 24.99 },
        salePrice: null,
        stockStatus: 'IN_STOCK',
        createdAt: new Date(),
        updatedAt: new Date(),
        description: 'A gentle cleanser',
        shortDescription: 'Gentle cleanser',
        stockQuantity: 100,
        type: 'SIMPLE',
        access: 'PUBLIC',
        password: null,
        saleStart: null,
        saleEnd: null,
        taxStatus: 'TAXABLE',
        taxClass: 'STANDARD',
        images: [{ id: 1, url: 'https://example.com/product1.jpg', position: 0 }],
        categories: [{ id: 101, name: 'Cleansers', slug: 'face-cleansers', imageUrl: 'https://example.com/cleansers.jpg' }],
        tags: [{ id: 1, name: 'Gentle', slug: 'gentle' }],
        listings: [{ id: 1, title: 'Listing 1', content: 'Content 1' }],
        ProductAttribute: [],
        variants: [],
      };

      jest.spyOn(prismaService.product, 'findUnique').mockResolvedValue(mockProduct);

      const result = await service.getProductDetails(1001);

      expect(result).toMatchObject({
        id: 1001,
        sku: 'FC-0001',
        name: 'Gentle Foaming Cleanser',
        slug: 'gentle-foaming-cleanser',
        price: 24.99,
        salePrice: null,
        stockStatus: 'IN_STOCK',
        access: 'PUBLIC',
      });
      expect(result.images).toHaveLength(1);
      expect(result.categories).toHaveLength(1);
      expect(result.tags).toHaveLength(1);
      expect(result.listings).toHaveLength(1);
    });

    it('should throw NotFoundException if product not found', async () => {
      jest.spyOn(prismaService.product, 'findUnique').mockResolvedValue(null);

      await expect(service.getProductDetails(999)).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if product is private', async () => {
      const mockProduct = {
        id: 1002,
        sku: 'FC-0002',
        name: 'Private Cleanser',
        price: { toNumber: () => 29.99 },
        access: 'PRIVATE',
        // ... other required fields
        createdAt: new Date(),
        updatedAt: new Date(),
        description: 'A private cleanser',
        shortDescription: 'Private cleanser',
        stockQuantity: 100,
        stockStatus: 'IN_STOCK',
        type: 'SIMPLE',
        password: null,
        salePrice: null,
        saleStart: null,
        saleEnd: null,
        taxStatus: 'TAXABLE',
        taxClass: 'STANDARD',
        images: [],
        categories: [],
        tags: [],
        listings: [],
        ProductAttribute: [],
        variants: [],
      };

      jest.spyOn(prismaService.product, 'findUnique').mockResolvedValue(mockProduct);

      await expect(service.getProductDetails(1002)).rejects.toThrow(NotFoundException);
    });

    it('should return limited info for protected product without password', async () => {
      const mockProduct = {
        id: 1003,
        sku: 'FC-0003',
        name: 'Protected Cleanser',
        price: { toNumber: () => 34.99 },
        access: 'PROTECTED',
        password: 'secret123',
        // ... other required fields
        createdAt: new Date(),
        updatedAt: new Date(),
        description: 'A protected cleanser',
        shortDescription: 'Protected cleanser',
        stockQuantity: 100,
        stockStatus: 'IN_STOCK',
        type: 'SIMPLE',
        salePrice: null,
        saleStart: null,
        saleEnd: null,
        taxStatus: 'TAXABLE',
        taxClass: 'STANDARD',
        images: [{ id: 1, url: 'https://example.com/product3.jpg', position: 0 }],
        categories: [{ id: 101, name: 'Cleansers', slug: 'face-cleansers', imageUrl: 'https://example.com/cleansers.jpg' }],
        tags: [{ id: 1, name: 'Protected', slug: 'protected' }],
        listings: [{ id: 1, title: 'Listing 1', content: 'Content 1' }],
        ProductAttribute: [],
        variants: [],
      };

      jest.spyOn(prismaService.product, 'findUnique').mockResolvedValue(mockProduct);

      const result = await service.getProductDetails(1003);

      expect(result).toMatchObject({
        id: 1003,
        sku: 'FC-0003',
        name: 'Protected Cleanser',
        slug: 'protected-cleanser',
        price: 34.99,
        access: 'PROTECTED',
      });
      expect(result.images).toHaveLength(1);
      expect(result.categories).toHaveLength(1);
      expect(result.tags).toHaveLength(0); // Limited info
      expect(result.listings).toHaveLength(0); // Limited info
    });
  });
});
