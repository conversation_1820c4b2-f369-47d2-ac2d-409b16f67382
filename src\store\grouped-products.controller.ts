import { Controller, Get, Post, Put, Body, Param, ParseIntPipe, Delete } from '@nestjs/common';
import { CreateAttributeDto } from './dto/create-attribute.dto';
import { Attribute, Product } from '@prisma/client';
import { CreateGroupedProductDto } from './dto/create-grouped-product.dto';
import { UpdateGroupedProductDto } from './dto/update-grouped-product.dto';
import { UpdateVariantDto } from './dto/update-variant.dto';
import { GroupedProductsService } from './grouped-products.service';

@Controller('store')
export class GroupedProductsController {
  constructor(private readonly groupedProductsService: GroupedProductsService) {}

  @Post('grouped-products/attributes')
  createAttribute(@Body() createAttributeDto: CreateAttributeDto): Promise<Attribute> {
    return this.groupedProductsService.createAttribute(createAttributeDto);
  }

  @Get('grouped-products/attributes')
  getAllAttributes(): Promise<Attribute[]> {
    return this.groupedProductsService.getAllAttributes();
  }

  @Get('grouped-products/attributes/:id')
  getAttributeById(@Param('id', ParseIntPipe) id: number): Promise<Attribute> {
    return this.groupedProductsService.getAttributeById(id);
  }

  @Post('grouped-products')
  createGroupedProduct(@Body() createProductDto: CreateGroupedProductDto): Promise<Product> {
    return this.groupedProductsService.createGroupedProduct(createProductDto);
  }

  @Post('grouped-products/categories/:categoryId')
  createGroupedProductForCategory(
    @Param('categoryId', ParseIntPipe) categoryId: number,
    @Body() createProductDto: CreateGroupedProductDto
  ): Promise<Product> {
    return this.groupedProductsService.createGroupedProductForCategory(categoryId, createProductDto);
  }

  @Get('grouped-products/:id')
  getGroupedProductById(@Param('id', ParseIntPipe) id: number): Promise<Product> {
    return this.groupedProductsService.getGroupedProductById(id);
  }

  @Put('grouped-products/:id')
  updateGroupedProduct(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProductDto: UpdateGroupedProductDto
  ): Promise<Product> {
    return this.groupedProductsService.updateGroupedProduct(id, updateProductDto);
  }

  @Put('grouped-products/:id/base')
  updateGroupedProductBase(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProductDto: UpdateGroupedProductDto
  ): Promise<Product> {
    return this.groupedProductsService.updateGroupedProductBase(id, updateProductDto);
  }

  @Put('grouped-products/:productId/variants/:variantId')
  updateGroupedProductVariant(
    @Param('productId', ParseIntPipe) productId: number,
    @Param('variantId', ParseIntPipe) variantId: number,
    @Body() updateVariantDto: UpdateVariantDto
  ): Promise<Product> {
    return this.groupedProductsService.updateGroupedProductVariant(productId, variantId, updateVariantDto);
  }

  @Delete('grouped-products/:id')
  deleteGroupedProduct(@Param('id', ParseIntPipe) id: number): Promise<Product> {
    return this.groupedProductsService.deleteGroupedProduct(id);
  }
}
