const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Setting up PostgreSQL search functionality...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../../prisma/setup-fulltext-search.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL content into individual statements
    const statements = sqlContent
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');
    
    // Execute each statement
    for (const statement of statements) {
      try {
        await prisma.$executeRawUnsafe(statement);
        console.log('Executed SQL statement successfully');
      } catch (error) {
        console.error(`Error executing SQL statement: ${statement}`);
        console.error(error);
      }
    }
    
    console.log('PostgreSQL search setup completed successfully!');
  } catch (error) {
    console.error('Error setting up PostgreSQL search:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
