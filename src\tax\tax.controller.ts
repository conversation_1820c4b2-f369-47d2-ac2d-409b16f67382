import { Controller, Get, Put, Body, UseGuards } from '@nestjs/common';
import { TaxService } from './tax.service';
import { UpdateTaxSettingsDto } from './dto/update-tax-settings.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@Controller('tax')
export class TaxController {
  constructor(private readonly taxService: TaxService) {}

  @Get()
  async getTaxSettings() {
    return this.taxService.getTaxSettings();
  }

  @Put()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async updateTaxSettings(@Body() updateTaxSettingsDto: UpdateTaxSettingsDto) {
    return this.taxService.updateTaxSettings(updateTaxSettingsDto);
  }
}
