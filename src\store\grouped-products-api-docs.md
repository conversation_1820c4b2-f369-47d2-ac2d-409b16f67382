# Grouped Products API Documentation

This document provides detailed information about the Grouped Products API endpoints available in the Coco Jojo Backend.

## Base URL

All API endpoints are prefixed with `/api/store`.

## Grouped Products Endpoints

### Create a Grouped Product

Creates a new grouped product with variants, attributes, images, and other related data.

**Endpoint:**
```
POST /grouped-products
```

**Request Body:**
```json
{
  "sku": "GP-12345",
  "name": "T-Shirt Collection",
  "description": "A collection of t-shirts in different colors and sizes",
  "shortDescription": "T-Shirt collection with variants",
  "price": "29.99",
  "salePrice": "24.99",
  "saleStart": "2023-06-01T00:00:00Z",
  "saleEnd": "2023-07-01T00:00:00Z",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "access": "PUBLIC",
  "categoryIds": [1, 2],
  "images": [
    {
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 1
    }
  ],
  "tags": ["clothing", "t-shirt", "summer"],
  "productAttributes": [
    {
      "attributeId": 1,
      "values": [
        { "value": "Small", "id": "temp-size-s" },
        { "value": "Medium", "id": "temp-size-m" },
        { "value": "Large", "id": "temp-size-l" }
      ]
    },
    {
      "attributeId": 2,
      "values": [
        { "value": "Red", "id": "temp-color-red" },
        { "value": "Blue", "id": "temp-color-blue" },
        { "value": "Green", "id": "temp-color-green" }
      ]
    }
  ],
  "variants": [
    {
      "sku": "GP-12345-S-RED",
      "price": "29.99",
      "stockQuantity": 30,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": ["temp-size-s", "temp-color-red"],
      "images": [
        {
          "url": "https://example.com/images/tshirt-s-red.jpg",
          "position": 1
        }
      ]
    },
    {
      "sku": "GP-12345-M-BLUE",
      "price": "29.99",
      "stockQuantity": 25,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": ["temp-size-m", "temp-color-blue"],
      "images": [
        {
          "url": "https://example.com/images/tshirt-m-blue.jpg",
          "position": 1
        }
      ]
    }
  ],
  "listings": [
    {
      "title": "Summer T-Shirt Collection",
      "content": "Our latest collection of summer t-shirts in various colors and sizes."
    }
  ]
}
```

**Response:**
```json
{
  "id": 1,
  "sku": "GP-12345",
  "name": "T-Shirt Collection",
  "description": "A collection of t-shirts in different colors and sizes",
  "shortDescription": "T-Shirt collection with variants",
  "price": "29.99",
  "salePrice": "24.99",
  "saleStart": "2023-06-01T00:00:00Z",
  "saleEnd": "2023-07-01T00:00:00Z",
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "access": "PUBLIC",
  "slug": "t-shirt-collection-123456",
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 1,
      "productId": 1,
      "variantId": null
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Clothing"
    },
    {
      "id": 2,
      "name": "T-Shirts"
    }
  ],
  "tags": [
    {
      "id": 1,
      "name": "clothing"
    },
    {
      "id": 2,
      "name": "t-shirt"
    },
    {
      "id": 3,
      "name": "summer"
    }
  ],
  "listings": [
    {
      "id": 1,
      "title": "Summer T-Shirt Collection",
      "content": "Our latest collection of summer t-shirts in various colors and sizes.",
      "productId": 1
    }
  ],
  "ProductAttribute": [
    {
      "id": 1,
      "productId": 1,
      "attributeId": 1,
      "attribute": {
        "id": 1,
        "name": "Size"
      },
      "values": [
        {
          "id": 1,
          "productAttributeId": 1,
          "value": "Small"
        },
        {
          "id": 2,
          "productAttributeId": 1,
          "value": "Medium"
        },
        {
          "id": 3,
          "productAttributeId": 1,
          "value": "Large"
        }
      ]
    },
    {
      "id": 2,
      "productId": 1,
      "attributeId": 2,
      "attribute": {
        "id": 2,
        "name": "Color"
      },
      "values": [
        {
          "id": 4,
          "productAttributeId": 2,
          "value": "Red"
        },
        {
          "id": 5,
          "productAttributeId": 2,
          "value": "Blue"
        },
        {
          "id": 6,
          "productAttributeId": 2,
          "value": "Green"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "sku": "GP-12345-S-RED",
      "price": "29.99",
      "stockQuantity": 30,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "id": 2,
          "url": "https://example.com/images/tshirt-s-red.jpg",
          "position": 1,
          "productId": null,
          "variantId": 1
        }
      ],
      "attributes": [
        {
          "variantId": 1,
          "valueId": 1,
          "value": {
            "id": 1,
            "productAttributeId": 1,
            "value": "Small"
          }
        },
        {
          "variantId": 1,
          "valueId": 4,
          "value": {
            "id": 4,
            "productAttributeId": 2,
            "value": "Red"
          }
        }
      ]
    },
    {
      "id": 2,
      "sku": "GP-12345-M-BLUE",
      "price": "29.99",
      "stockQuantity": 25,
      "stockStatus": "IN_STOCK",
      "productId": 1,
      "ProductImage": [
        {
          "id": 3,
          "url": "https://example.com/images/tshirt-m-blue.jpg",
          "position": 1,
          "productId": null,
          "variantId": 2
        }
      ],
      "attributes": [
        {
          "variantId": 2,
          "valueId": 2,
          "value": {
            "id": 2,
            "productAttributeId": 1,
            "value": "Medium"
          }
        },
        {
          "variantId": 2,
          "valueId": 5,
          "value": {
            "id": 5,
            "productAttributeId": 2,
            "value": "Blue"
          }
        }
      ]
    }
  ]
}
```

### Update a Grouped Product

Updates an existing grouped product with all its related data including variants, attributes, images, etc.

**Endpoint:**
```
PUT /grouped-products/:id
```

**Request Body:**
```json
{
  "name": "Updated T-Shirt Collection",
  "description": "Updated description for the t-shirt collection",
  "price": "34.99",
  "stockStatus": "IN_STOCK",
  "categoryIds": [1, 2, 3],
  "images": [
    {
      "id": 1,
      "url": "https://example.com/images/tshirt-main-updated.jpg",
      "position": 1
    },
    {
      "url": "https://example.com/images/tshirt-new-image.jpg",
      "position": 2
    }
  ],
  "tags": ["clothing", "t-shirt", "summer", "updated"],
  "productAttributes": [
    {
      "id": 1,
      "values": [
        { "id": 1, "value": "Small" },
        { "id": 2, "value": "Medium" },
        { "id": 3, "value": "Large" },
        { "value": "X-Large", "id": "temp-size-xl" }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "price": "34.99",
      "stockQuantity": 25,
      "images": [
        {
          "id": 2,
          "url": "https://example.com/images/tshirt-s-red-updated.jpg"
        }
      ]
    },
    {
      "id": 2,
      "delete": true
    },
    {
      "sku": "GP-12345-XL-RED",
      "price": "39.99",
      "stockQuantity": 15,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": ["temp-size-xl", 4],
      "images": [
        {
          "url": "https://example.com/images/tshirt-xl-red.jpg",
          "position": 1
        }
      ]
    }
  ],
  "listings": [
    {
      "id": 1,
      "title": "Updated Summer T-Shirt Collection",
      "content": "Our updated collection of summer t-shirts in various colors and sizes."
    },
    {
      "title": "New Listing for T-Shirt Collection",
      "content": "Additional listing content for the t-shirt collection."
    }
  ]
}
```

**Response:**
The response will be similar to the create endpoint, containing the full updated grouped product with all its relationships.

### Get a Grouped Product

Retrieves a grouped product by its ID.

**Endpoint:**
```
GET /grouped-products/:id
```

**Response:**
The response will contain the full grouped product with all its relationships, similar to the create endpoint response.

### Delete a Grouped Product

Deletes a grouped product and all its related data.

**Endpoint:**
```
DELETE /grouped-products/:id
```

**Response:**
The response will contain the deleted grouped product data.

## Attributes Endpoints

### Create an Attribute

Creates a new attribute that can be used for grouped products.

**Endpoint:**
```
POST /grouped-products/attributes
```

**Request Body:**
```json
{
  "name": "Size"
}
```

**Response:**
```json
{
  "id": 1,
  "name": "Size"
}
```

### Get All Attributes

Retrieves all attributes.

**Endpoint:**
```
GET /grouped-products/attributes
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "Size"
  },
  {
    "id": 2,
    "name": "Color"
  }
]
```

### Get an Attribute by ID

Retrieves an attribute by its ID.

**Endpoint:**
```
GET /grouped-products/attributes/:id
```

**Response:**
```json
{
  "id": 1,
  "name": "Size"
}
```
