import { IsNotEmpty, IsString, IsOptional, IsArray, IsEnum, IsInt, IsPositive, IsDateString, IsDecimal, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { AccessLevel, ProductType, StockStatus, TaxClass, TaxStatus } from '@prisma/client';
import { CreateListingDto } from './create-listing.dto';

export class CreateProductDto {
  @IsNotEmpty()
  @IsString()
  sku: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  shortDescription?: string;

  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '2' })
  price: string; // Using string for Decimal

  @IsOptional()
  @IsDecimal({ decimal_digits: '2' })
  salePrice?: string; // Using string for Decimal

  @IsOptional()
  @IsDateString()
  saleStart?: string;

  @IsOptional()
  @IsDateString()
  saleEnd?: string;

  @IsOptional()
  @IsInt()
  @IsPositive()
  stockQuantity?: number;

  @IsNotEmpty()
  @IsEnum(StockStatus)
  stockStatus: StockStatus;

  @IsOptional()
  @IsEnum(TaxStatus)
  taxStatus?: TaxStatus;

  @IsOptional()
  @IsEnum(TaxClass)
  taxClass?: TaxClass;

  @IsOptional()
  @IsEnum(ProductType)
  type?: ProductType;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Type(() => Number)
  categoryIds?: number[];

  @IsOptional()
  @IsArray()
  images?: { url: string; position?: number }[];

  @IsOptional()
  @IsEnum(AccessLevel)
  access?: AccessLevel;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateListingDto)
  listings?: CreateListingDto[];

}


