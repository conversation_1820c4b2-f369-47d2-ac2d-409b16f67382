import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { MainCategoryDto } from './dto/main-category.dto';
import { SubcategoriesDto } from './dto/subcategories.dto';
import { PaginatedProductsDto, EnhancedPaginatedProductsDto } from './dto/paginated-products.dto';
import { ProductDetailsDto } from './dto/product-details.dto';
import { AccessLevel } from '@prisma/client';

@Injectable()
export class ShopService {
  constructor(private prisma: PrismaService) {}

  async getMainCategories(): Promise<{ data: MainCategoryDto[] }> {
    // Get all main categories
    const mainCategories = await this.prisma.mainCategory.findMany();

    // For each main category, count the products in its subcategories
    const mainCategoriesWithCount = await Promise.all(
      mainCategories.map(async (mainCategory) => {
        // Get all subcategories for this main category
        const subcategories = await this.prisma.category.findMany({
          where: { mainCategoryId: mainCategory.id },
          select: { id: true },
        });

        // Get the count of products for all subcategories
        const subcategoryIds = subcategories.map((cat) => cat.id);

        let count = 0;
        if (subcategoryIds.length > 0) {
          count = await this.prisma.productCategories.count({
            where: {
              categoryId: {
                in: subcategoryIds,
              },
            },
          });
        }

        return {
          id: mainCategory.id,
          name: mainCategory.name,
          slug: mainCategory.slug,
          imageUrl: mainCategory.imageUrl,
          count,
        };
      })
    );

    return { data: mainCategoriesWithCount };
  }

  async getSubcategories(mainId: number): Promise<SubcategoriesDto> {
    // Get the main category
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { id: mainId },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with ID ${mainId} not found`);
    }

    // Get all subcategories for this main category
    const subcategories = await this.prisma.category.findMany({
      where: { mainCategoryId: mainId },
    });

    // For each subcategory, count the products
    const subcategoriesWithCount = await Promise.all(
      subcategories.map(async (category) => {
        const count = await this.prisma.productCategories.count({
          where: { categoryId: category.id },
        });

        return {
          id: category.id,
          name: category.name,
          slug: category.slug,
          imageUrl: category.imageUrl,
          count,
          parentId: category.mainCategoryId,
        };
      })
    );

    return {
      mainCategory: {
        id: mainCategory.id,
        name: mainCategory.name,
        slug: mainCategory.slug,
      },
      subcategories: subcategoriesWithCount,
    };
  }

  async getProductsByCategory(
    categoryId: number,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedProductsDto> {
    // Check if category exists
    const category = await this.prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${categoryId} not found`);
    }

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Get total count of products in this category (excluding private products)
    const total = await this.prisma.productCategories.count({
      where: {
        categoryId,
        product: {
          access: {
            not: AccessLevel.PRIVATE
          }
        }
      },
    });

    // Get products for this category with pagination (excluding private products)
    const products = await this.prisma.product.findMany({
      where: {
        categories: {
          some: {
            id: categoryId,
          },
        },
        access: {
          not: AccessLevel.PRIVATE
        }
      },
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        createdAt: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
      },
      skip,
      take,
    });

    // Format the products to match the expected response
    const formattedProducts = products.map((product) => ({
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      imageUrl: product.images[0]?.url || null,
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      inStock: product.stockStatus === 'IN_STOCK',
      created_at: product.createdAt,
    }));

    return {
      pagination: {
        total,
        page,
        limit,
      },
      data: formattedProducts,
    };
  }

  // Helper method to generate slug from name
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-'); // Replace multiple hyphens with a single one
  }

  async getProductDetailsBySlug(slug: string, password?: string): Promise<ProductDetailsDto> {
    // Generate a normalized slug from the input
    const normalizedSlug = slug.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-');

    // Find the product directly by slug
    const product = await this.prisma.product.findFirst({
      where: {
        slug: normalizedSlug
      },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
        ProductAttribute: {
          include: {
            attribute: true,
            values: true,
          },
        },
        variants: {
          include: {
            ProductImage: true,
            attributes: {
              include: {
                value: true,
              },
            },
          },
        },
      },
    });
    // If product doesn't exist, throw NotFoundException
    if (!product) {
      throw new NotFoundException(`Product with slug "${slug}" not found`);
    }

    // Handle access control
    if (product.access === AccessLevel.PRIVATE) {
      throw new NotFoundException(`Product with slug "${slug}" not found`);
    }

    // For protected products, check password
    if (product.access === AccessLevel.PROTECTED) {
      // If no password provided or password doesn't match, return product with limited info
      if (!password || password !== product.password) {
        return {
          id: product.id,
          sku: product.sku,
          name: product.name,
          slug: this.generateSlug(product.name),
          description: product.description || '',
          shortDescription: product.shortDescription || '',
          price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
            ? product.price.toNumber()
            : Number(product.price),
          salePrice: product.salePrice ? Number(product.salePrice) : null,
          stockQuantity: product.stockQuantity || 0,
          stockStatus: product.stockStatus,
          type: product.type,
          access: product.access,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
          images: product.images.map(image => ({
            id: image.id,
            url: image.url,
            position: image.position,
          })),
          categories: product.categories.map(category => ({
            id: category.id,
            name: category.name,
            slug: category.slug,
            imageUrl: category.imageUrl,
          })),
          tags: [],
          listings: [],
          ProductAttribute: [],
          variants: [],
        };
      }
    }

    // Return full product details
    return {
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      description: product.description || '',
      shortDescription: product.shortDescription || '',
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      stockQuantity: product.stockQuantity || 0,
      stockStatus: product.stockStatus,
      type: product.type,
      access: product.access,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      images: product.images.map(image => ({
        id: image.id,
        url: image.url,
        position: image.position,
      })),
      categories: product.categories.map(category => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        imageUrl: category.imageUrl,
      })),
      tags: product.tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
      })),
      listings: product.listings.map(listing => ({
        id: listing.id,
        title: listing.title,
        content: listing.content,
      })),
      ProductAttribute: product.ProductAttribute.map(pa => ({
        attribute: {
          id: pa.attribute.id,
          name: pa.attribute.name,
          values: pa.values.map(v => ({
            id: v.id,
            value: v.value,
          })),
        },
        values: pa.values.map(v => ({
          id: v.id,
          value: v.value,
        })),
      })),
      variants: product.variants.map(variant => ({
        id: variant.id,
        sku: variant.sku,
        price: typeof variant.price === 'object' && variant.price !== null && 'toNumber' in variant.price
          ? variant.price.toNumber()
          : Number(variant.price),
        stockQuantity: variant.stockQuantity || 0,
        stockStatus: variant.stockStatus,
        ProductImage: variant.ProductImage.map(image => ({
          id: image.id,
          url: image.url,
          position: image.position,
        })),
        attributes: variant.attributes.map(attr => ({
          value: {
            id: attr.value.id,
            value: attr.value.value,
          },
        })),
      })),
    };
  }

  async getProductDetails(id: number, password?: string): Promise<ProductDetailsDto> {
    // Find the product with all its relationships
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
        ProductAttribute: {
          include: {
            attribute: true,
            values: true,
          },
        },
        variants: {
          include: {
            ProductImage: true,
            attributes: {
              include: {
                value: true,
              },
            },
          },
        },
      },
    });

    // If product doesn't exist, throw NotFoundException
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    // Handle access control
    if (product.access === AccessLevel.PRIVATE) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    // For protected products, check password
    if (product.access === AccessLevel.PROTECTED) {
      // If no password provided or password doesn't match, return product with limited info
      if (!password || password !== product.password) {
        return {
          id: product.id,
          sku: product.sku,
          name: product.name,
          slug: this.generateSlug(product.name),
          description: product.description || '',
          shortDescription: product.shortDescription || '',
          price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
            ? product.price.toNumber()
            : Number(product.price),
          salePrice: product.salePrice ? Number(product.salePrice) : null,
          stockQuantity: product.stockQuantity || 0,
          stockStatus: product.stockStatus,
          type: product.type,
          access: product.access,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
          images: product.images.map(image => ({
            id: image.id,
            url: image.url,
            position: image.position,
          })),
          categories: product.categories.map(category => ({
            id: category.id,
            name: category.name,
            slug: category.slug,
            imageUrl: category.imageUrl,
          })),
          tags: [],
          listings: [],
          ProductAttribute: [],
          variants: [],
        };
      }
    }

    // Return full product details
    return {
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      description: product.description || '',
      shortDescription: product.shortDescription || '',
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      stockQuantity: product.stockQuantity || 0,
      stockStatus: product.stockStatus,
      type: product.type,
      access: product.access,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      images: product.images.map(image => ({
        id: image.id,
        url: image.url,
        position: image.position,
      })),
      categories: product.categories.map(category => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        imageUrl: category.imageUrl,
      })),
      tags: product.tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        slug: tag.slug,
      })),
      listings: product.listings.map(listing => ({
        id: listing.id,
        title: listing.title,
        content: listing.content,
      })),
      ProductAttribute: product.ProductAttribute.map(pa => ({
        attribute: {
          id: pa.attribute.id,
          name: pa.attribute.name,
          values: pa.values.map(v => ({
            id: v.id,
            value: v.value,
          })),
        },
        values: pa.values.map(v => ({
          id: v.id,
          value: v.value,
        })),
      })),
      variants: product.variants.map(variant => ({
        id: variant.id,
        sku: variant.sku,
        price: typeof variant.price === 'object' && variant.price !== null && 'toNumber' in variant.price
          ? variant.price.toNumber()
          : Number(variant.price),
        stockQuantity: variant.stockQuantity || 0,
        stockStatus: variant.stockStatus,
        ProductImage: variant.ProductImage.map(image => ({
          id: image.id,
          url: image.url,
          position: image.position,
        })),
        attributes: variant.attributes.map(attr => ({
          value: {
            id: attr.value.id,
            value: attr.value.value,
          },
        })),
      })),
    };
  }

  async searchProducts(query: string, limit = 20, page = 1) {
    const skip = (page - 1) * limit;
    const limitInt = parseInt(limit.toString(), 10);

    // Clean and prepare the search query
    const cleanQuery = query.trim().toLowerCase();

    if (!cleanQuery) {
      return {
        data: [],
        pagination: {
          total: 0,
          page: parseInt(page.toString(), 10),
          limit: limitInt
        }
      };
    }

    // Use raw SQL for advanced search with search vector and trigram similarity
    const searchResults = await this.prisma.$queryRaw`
      SELECT
        p.id,
        p.name,
        p.slug,
        p.price,
        p.access,
        (
          -- Full-text search score (highest weight)
          COALESCE(ts_rank(p.search_vector, plainto_tsquery('english', ${cleanQuery})), 0) * 4 +
          -- Trigram similarity for name (very high weight)
          COALESCE(similarity(p.name, ${cleanQuery}), 0) * 5 +
          -- Trigram similarity for normalized name (high weight for fuzzy matching)
          COALESCE(similarity(normalize_text(p.name), normalize_text(${cleanQuery})), 0) * 4 +
          -- Trigram similarity for description
          COALESCE(similarity(COALESCE(p.description, ''), ${cleanQuery}), 0) * 2 +
          -- Trigram similarity for short description
          COALESCE(similarity(COALESCE(p."shortDescription", ''), ${cleanQuery}), 0) * 2 +
          -- Exact match bonus
          CASE
            WHEN LOWER(p.name) LIKE LOWER(${`%${cleanQuery}%`}) THEN 3
            WHEN normalize_text(p.name) LIKE normalize_text(${`%${cleanQuery}%`}) THEN 2
            ELSE 0
          END
        ) as relevance_score
      FROM "Product" p
      WHERE
        p.access != 'PRIVATE'
        AND (
          -- Full-text search using search vector
          p.search_vector @@ plainto_tsquery('english', ${cleanQuery})
          OR
          -- Trigram similarity search with lower threshold for better fuzzy matching
          similarity(p.name, ${cleanQuery}) > 0.1
          OR
          similarity(normalize_text(p.name), normalize_text(${cleanQuery})) > 0.1
          OR
          similarity(COALESCE(p.description, ''), ${cleanQuery}) > 0.1
          OR
          similarity(COALESCE(p."shortDescription", ''), ${cleanQuery}) > 0.1
          OR
          -- Fallback: case-insensitive contains
          LOWER(p.name) LIKE LOWER(${`%${cleanQuery}%`})
          OR
          normalize_text(p.name) LIKE normalize_text(${`%${cleanQuery}%`})
          OR
          LOWER(COALESCE(p.description, '')) LIKE LOWER(${`%${cleanQuery}%`})
          OR
          LOWER(COALESCE(p."shortDescription", '')) LIKE LOWER(${`%${cleanQuery}%`})
        )
      ORDER BY relevance_score DESC, p.name ASC
      LIMIT ${limitInt}
      OFFSET ${skip}
    ` as Array<{
      id: number;
      name: string;
      slug: string;
      price: any;
      access: string;
      relevance_score: number;
    }>;

    // Get total count with the same search criteria
    const totalCountResult = await this.prisma.$queryRaw`
      SELECT COUNT(*) as total
      FROM "Product" p
      WHERE
        p.access != 'PRIVATE'
        AND (
          p.search_vector @@ plainto_tsquery('english', ${cleanQuery})
          OR
          similarity(p.name, ${cleanQuery}) > 0.1
          OR
          similarity(normalize_text(p.name), normalize_text(${cleanQuery})) > 0.1
          OR
          similarity(COALESCE(p.description, ''), ${cleanQuery}) > 0.1
          OR
          similarity(COALESCE(p."shortDescription", ''), ${cleanQuery}) > 0.1
          OR
          LOWER(p.name) LIKE LOWER(${`%${cleanQuery}%`})
          OR
          normalize_text(p.name) LIKE normalize_text(${`%${cleanQuery}%`})
          OR
          LOWER(COALESCE(p.description, '')) LIKE LOWER(${`%${cleanQuery}%`})
          OR
          LOWER(COALESCE(p."shortDescription", '')) LIKE LOWER(${`%${cleanQuery}%`})
        )
    ` as Array<{ total: bigint }>;

    const totalCount = Number(totalCountResult[0]?.total || 0);

    // Get product images for the found products
    const productIds = searchResults.map(p => p.id);
    const productImages = productIds.length > 0 ? await this.prisma.productImage.findMany({
      where: {
        productId: {
          in: productIds
        }
      },
      orderBy: {
        position: 'asc'
      }
    }) : [];

    // Group images by product ID
    const imagesByProduct = productImages.reduce((acc, image) => {
      if (!acc[image.productId!]) {
        acc[image.productId!] = [];
      }
      acc[image.productId!].push(image);
      return acc;
    }, {} as Record<number, typeof productImages>);

    return {
      data: searchResults.map(product => {
        const images = imagesByProduct[product.id] || [];
        return {
          id: product.id.toString(),
          name: product.name,
          slug: product.slug,
          price: {
            price: Number(product.price)
          },
          media: {
            mainMedia: {
              image: {
                url: images[0]?.url || 'https://placehold.co/600x400?text=No+Image'
              }
            },
            items: images.map(image => ({
              image: {
                url: image.url
              }
            }))
          },
          score: Number(product.relevance_score)
        };
      }),
      pagination: {
        total: totalCount,
        page: parseInt(page.toString(), 10),
        limit: limitInt
      }
    };
  }

  async getProductsByMainCategoryAndCategorySlug(
    mainCategorySlug: string,
    categorySlug: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<EnhancedPaginatedProductsDto> {
    // Find the main category by slug
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { slug: mainCategorySlug },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with slug "${mainCategorySlug}" not found`);
    }

    // Find the category by slug and main category
    const category = await this.prisma.category.findFirst({
      where: {
        slug: categorySlug,
        mainCategoryId: mainCategory.id,
      },
    });

    if (!category) {
      throw new NotFoundException(`Category with slug "${categorySlug}" not found in main category "${mainCategorySlug}"`);
    }

    // Get all subcategories for this main category with their product counts
    const subcategories = await this.prisma.category.findMany({
      where: { mainCategoryId: mainCategory.id },
      select: {
        id: true,
        name: true,
        slug: true,
        imageUrl: true,
      },
    });

    // Get product counts for each subcategory
    const subcategoriesWithCount = await Promise.all(
      subcategories.map(async (cat) => {
        const count = await this.prisma.productCategories.count({
          where: {
            categoryId: cat.id,
            product: {
              access: {
                not: AccessLevel.PRIVATE
              }
            }
          },
        });

        return {
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
          imageUrl: cat.imageUrl,
          count,
        };
      })
    );

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Get total count of products in this category (excluding private products)
    const total = await this.prisma.productCategories.count({
      where: {
        categoryId: category.id,
        product: {
          access: {
            not: AccessLevel.PRIVATE
          }
        }
      },
    });

    // Get products for this category with pagination (excluding private products)
    const products = await this.prisma.product.findMany({
      where: {
        categories: {
          some: {
            id: category.id,
          },
        },
        access: {
          not: AccessLevel.PRIVATE
        }
      },
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        createdAt: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
      },
      skip,
      take,
    });

    // Format the products to match the expected response
    const formattedProducts = products.map((product) => ({
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      imageUrl: product.images[0]?.url || null,
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      inStock: product.stockStatus === 'IN_STOCK',
      created_at: product.createdAt,
    }));

    return {
      pagination: {
        total,
        page,
        limit,
      },
      data: formattedProducts,
      categories: subcategoriesWithCount,
    };
  }

  async getProductsByMainCategorySlug(
    mainCategorySlug: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<EnhancedPaginatedProductsDto> {
    // Find the main category by slug
    const mainCategory = await this.prisma.mainCategory.findUnique({
      where: { slug: mainCategorySlug },
    });

    if (!mainCategory) {
      throw new NotFoundException(`Main category with slug "${mainCategorySlug}" not found`);
    }

    // Get all subcategories for this main category with their product counts
    const subcategories = await this.prisma.category.findMany({
      where: { mainCategoryId: mainCategory.id },
      select: {
        id: true,
        name: true,
        slug: true,
        imageUrl: true,
      },
    });

    // Get product counts for each subcategory
    const subcategoriesWithCount = await Promise.all(
      subcategories.map(async (category) => {
        const count = await this.prisma.productCategories.count({
          where: {
            categoryId: category.id,
            product: {
              access: {
                not: AccessLevel.PRIVATE
              }
            }
          },
        });

        return {
          id: category.id,
          name: category.name,
          slug: category.slug,
          imageUrl: category.imageUrl,
          count,
        };
      })
    );

    const subcategoryIds = subcategories.map(cat => cat.id);

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Get total count of products in these categories (excluding private products)
    const total = await this.prisma.productCategories.count({
      where: {
        categoryId: {
          in: subcategoryIds,
        },
        product: {
          access: {
            not: AccessLevel.PRIVATE
          }
        }
      },
    });

    // Get products for these categories with pagination (excluding private products)
    const products = await this.prisma.product.findMany({
      where: {
        categories: {
          some: {
            id: {
              in: subcategoryIds,
            },
          },
        },
        access: {
          not: AccessLevel.PRIVATE
        }
      },
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        createdAt: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
      },
      skip,
      take,
    });

    // Format the products to match the expected response
    const formattedProducts = products.map((product) => ({
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      imageUrl: product.images[0]?.url || null,
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      inStock: product.stockStatus === 'IN_STOCK',
      created_at: product.createdAt,
    }));

    return {
      pagination: {
        total,
        page,
        limit,
      },
      data: formattedProducts,
      categories: subcategoriesWithCount,
    };
  }

  async getProductsByCategorySlug(
    slug: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedProductsDto> {
    // Find the category by slug
    const category = await this.prisma.category.findUnique({
      where: { slug },
    });

    if (!category) {
      throw new NotFoundException(`Category with slug "${slug}" not found`);
    }

    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Get total count of products in this category (excluding private products)
    const total = await this.prisma.productCategories.count({
      where: {
        categoryId: category.id,
        product: {
          access: {
            not: AccessLevel.PRIVATE
          }
        }
      },
    });

    // Get products for this category with pagination (excluding private products)
    const products = await this.prisma.product.findMany({
      where: {
        categories: {
          some: {
            id: category.id,
          },
        },
        access: {
          not: AccessLevel.PRIVATE
        }
      },
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        createdAt: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
      },
      skip,
      take,
    });

    // Format the products to match the expected response
    const formattedProducts = products.map((product) => ({
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      imageUrl: product.images[0]?.url || null,
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      inStock: product.stockStatus === 'IN_STOCK',
      created_at: product.createdAt,
    }));

    return {
      pagination: {
        total,
        page,
        limit,
      },
      data: formattedProducts,
    };
  }

  async getAllProducts(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedProductsDto> {
    // Calculate pagination values
    const skip = (page - 1) * limit;
    const take = Number(limit);

    // Get total count of products (excluding private products)
    const total = await this.prisma.product.count({
      where: {
        access: {
          not: AccessLevel.PRIVATE
        }
      },
    });

    // Get all products with pagination (excluding private products)
    const products = await this.prisma.product.findMany({
      where: {
        access: {
          not: AccessLevel.PRIVATE
        }
      },
      select: {
        id: true,
        sku: true,
        name: true,
        price: true,
        salePrice: true,
        stockStatus: true,
        createdAt: true,
        images: {
          take: 1,
          select: {
            url: true,
          },
        },
      },
      skip,
      take,
    });

    // Format the products to match the expected response
    const formattedProducts = products.map((product) => ({
      id: product.id,
      sku: product.sku,
      name: product.name,
      slug: this.generateSlug(product.name),
      imageUrl: product.images[0]?.url || null,
      price: typeof product.price === 'object' && product.price !== null && 'toNumber' in product.price
        ? product.price.toNumber()
        : Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      inStock: product.stockStatus === 'IN_STOCK',
      created_at: product.createdAt,
    }));

    return {
      pagination: {
        total,
        page,
        limit,
      },
      data: formattedProducts,
    };
  }

  async getAllProductNames(): Promise<{ data: { id: number; name: string; slug: string }[] }> {
    // Get all non-private products with minimal fields
    const products = await this.prisma.product.findMany({
      where: {
        access: {
          not: AccessLevel.PRIVATE
        }
      },
      select: {
        id: true,
        name: true,
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Format the response
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      slug: this.generateSlug(product.name)
    }));

    return { data: formattedProducts };
  }
}
