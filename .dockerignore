# Git
.git
.gitignore

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Build output
dist
build

# Environment variables
.env
.env.*
!.env.example

# IDE files
.idea
.vscode
*.sublime-project
*.sublime-workspace

# Logs
logs
*.log

# OS specific
.DS_Store
Thumbs.db

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Tests
test
coverage
.nyc_output

# Misc
README.md
LICENSE
.github
