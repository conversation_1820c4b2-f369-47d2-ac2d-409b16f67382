#!/bin/bash

# Configuration
DOCKERHUB_USERNAME="yourusername"  # Replace with your Docker Hub username
APP_NAME="coco-jojo-backend"
TAG="latest"

# Build the Docker image
echo "Building Docker image: $DOCKERHUB_USERNAME/$APP_NAME:$TAG"
docker build -t "$DOCKERHUB_USERNAME/$APP_NAME:$TAG" .

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "Error: Docker build failed"
    exit 1
fi

# Push the image to Docker Hub
echo "Pushing image to Docker Hub: $DOCKERHUB_USERNAME/$APP_NAME:$TAG"
docker push "$DOCKERHUB_USERNAME/$APP_NAME:$TAG"

# Check if push was successful
if [ $? -ne 0 ]; then
    echo "Error: Docker push failed. Make sure you're logged in with 'docker login'"
    exit 1
fi

echo "Successfully built and pushed Docker image: $DOCKERHUB_USERNAME/$APP_NAME:$TAG"
echo "Instructions for deployment can be found in DOCKER_DEPLOYMENT.md"
