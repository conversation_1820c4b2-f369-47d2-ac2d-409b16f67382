# API Documentation

This document provides examples of how to use the backend APIs correctly.

## Base URL

All API endpoints are prefixed with:
```
http://localhost:3300/api
```

## API Modules

The API is organized into the following modules:

1. **Store Module** (`/api/store/...`): Admin-facing APIs for managing products, categories, and other store data.
2. **Shop Module** (`/api/shop/...`): Customer-facing APIs for browsing products, categories, and other shop data.
3. **Product Sections Module** (`/api/store/product-sections/...`): APIs for managing product sections on the storefront.

For detailed documentation on specific modules, please refer to:
- [Shop API Documentation](../shop/shop-api-documentation.md)
- [Product Sections API Documentation](../product-sections/product-sections-api-documentation.md)

## Category APIs

### Get All Categories

**Endpoint:**
```
GET /api/store/categories
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/categories
```

**Example Response:**
```json
[
  {
    "id": 3,
    "name": "Skin Care",
    "slug": "skin-care",
    "description": "description for skin care category"
  }
]
```

### Get Category by ID

**Endpoint:**
```
GET /api/store/categories/:id
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/categories/3
```

**Example Response:**
```json
{
  "id": 3,
  "name": "Skin Care",
  "slug": "skin-care",
  "description": "description for skin care category"
}
```

### Create Category

**Endpoint:**
```
POST /api/store/categories
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/store/categories \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Electronics",
    "slug": "electronics",
    "description": "Electronic devices and accessories"
  }'
```

**Example Response:**
```json
{
  "id": 4,
  "name": "Electronics",
  "slug": "electronics",
  "description": "Electronic devices and accessories"
}
```

### Update Category

**Endpoint:**
```
PATCH /api/store/categories/:id
```

**Example Request:**
```bash
curl -X PATCH http://localhost:3300/api/store/categories/3 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Skin Care"
  }'
```

**Example Response:**
```json
{
  "id": 3,
  "name": "Updated Skin Care",
  "slug": "skin-care",
  "description": "description for skin care category"
}
```

### Delete Category

**Endpoint:**
```
DELETE /api/store/categories/:id
```

**Example Request:**
```bash
curl -X DELETE http://localhost:3300/api/store/categories/4
```

**Example Response:**
```json
{
  "id": 4,
  "name": "Electronics",
  "slug": "electronics",
  "description": "Electronic devices and accessories"
}
```

## Product APIs

### Get All Products

**Endpoint:**
```
GET /api/store/products
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/products
```

**Example Response:**
```json
[
  {
    "id": 1,
    "name": "Hair brush",
    "shortDescription": "the best hair brush",
    "price": "15",
    "sku": "hair-brush-1",
    "stockQuantity": 12,
    "stockStatus": "IN_STOCK",
    "images": [
      {
        "url": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
      }
    ]
  }
]
```

### Get Product by ID (Admin-Facing)

Retrieves detailed information about a specific product for administrative purposes. This endpoint does not enforce access control rules and is intended for admin use only. For customer-facing product details with access control, use the `/api/shop/products/:id` endpoint instead.

**Endpoint:**
```
GET /api/store/products/:id
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/products/1
```

**Example Response:**
```json
{
  "id": 1,
  "createdAt": "2025-04-26T13:20:45.057Z",
  "updatedAt": "2025-04-26T13:20:45.057Z",
  "sku": "hair-brush-1",
  "name": "Hair brush",
  "description": "the best hair brush out there",
  "shortDescription": "the best hair brush",
  "price": "15",
  "salePrice": "12",
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": 12,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "images": [
    {
      "id": 1,
      "url": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg",
      "position": 0,
      "productId": 1
    }
  ],
  "categories": [
    {
      "id": 3,
      "name": "Skin Care",
      "slug": "skin-care",
      "description": "description for skin care category"
    }
  ]
}
```

### Get Products by Category

**Endpoint:**
```
GET /api/store/categories/:categoryId/products
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/categories/3/products
```

**Example Response:**
```json
[
  {
    "id": 1,
    "name": "Hair brush",
    "shortDescription": "the best hair brush",
    "price": "15",
    "sku": "hair-brush-1",
    "stockQuantity": 12,
    "stockStatus": "IN_STOCK",
    "images": [
      {
        "url": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg"
      }
    ]
  }
]
```

### Create Product

**Endpoint:**
```
POST /api/store/products
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/store/products \
  -H "Content-Type: application/json" \
  -d '{
    "sku": "prod-001",
    "name": "Product Name",
    "description": "Product description",
    "shortDescription": "Short description",
    "price": "99.99",
    "stockQuantity": 100,
    "categoryIds": [3],
    "access": "PUBLIC",
    "password": "optional-password-for-protected-products",
    "images": [
      {
        "url": "https://example.com/image.jpg",
        "position": 0
      }
    ]
  }'
```

**Example Response:**
```json
{
  "id": 2,
  "createdAt": "2025-04-26T15:30:00.000Z",
  "updatedAt": "2025-04-26T15:30:00.000Z",
  "sku": "prod-001",
  "name": "Product Name",
  "description": "Product description",
  "shortDescription": "Short description",
  "price": "99.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "access": "PUBLIC",
  "password": null,
  "images": [
    {
      "id": 2,
      "url": "https://example.com/image.jpg",
      "position": 0,
      "productId": 2
    }
  ],
  "categories": [
    {
      "id": 3,
      "name": "Skin Care",
      "slug": "skin-care",
      "description": "description for skin care category"
    }
  ]
}
```

### Create Product for Specific Category

**Endpoint:**
```
POST /api/store/categories/:categoryId/products
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/store/categories/3/products \
  -H "Content-Type: application/json" \
  -d '{
    "sku": "prod-002",
    "name": "Category-Specific Product",
    "description": "Product description",
    "shortDescription": "Short description",
    "price": "49.99",
    "stockQuantity": 50,
    "images": [
      {
        "url": "https://example.com/image.jpg",
        "position": 0
      }
    ]
  }'
```

**Example Response:**
```json
{
  "id": 3,
  "createdAt": "2025-04-26T15:35:00.000Z",
  "updatedAt": "2025-04-26T15:35:00.000Z",
  "sku": "prod-002",
  "name": "Category-Specific Product",
  "description": "Product description",
  "shortDescription": "Short description",
  "price": "49.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": 50,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "images": [
    {
      "id": 3,
      "url": "https://example.com/image.jpg",
      "position": 0,
      "productId": 3
    }
  ],
  "categories": [
    {
      "id": 3,
      "name": "Skin Care",
      "slug": "skin-care",
      "description": "description for skin care category"
    }
  ]
}
```

### Update Product

**Endpoint:**
```
PATCH /api/store/products/:id
```

**Example Request:**
```bash
curl -X PATCH http://localhost:3300/api/store/products/1 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Hair Brush",
    "price": "19.99",
    "categoryIds": [3],
    "access": "PROTECTED",
    "password": "my-protected-product-password"
  }'
```

**Example Response:**
```json
{
  "id": 1,
  "createdAt": "2025-04-26T13:20:45.057Z",
  "updatedAt": "2025-04-26T15:40:00.000Z",
  "sku": "hair-brush-1",
  "name": "Updated Hair Brush",
  "description": "the best hair brush out there",
  "shortDescription": "the best hair brush",
  "price": "19.99",
  "salePrice": "12",
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": 12,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "access": "PROTECTED",
  "password": "my-protected-product-password",
  "images": [
    {
      "id": 1,
      "url": "https://as2.ftcdn.net/v2/jpg/02/47/10/83/1000_F_247108374_YoA2PpgwWZKQ5x7Q2CegWffwLUO6VLbt.jpg",
      "position": 0,
      "productId": 1
    }
  ],
  "categories": [
    {
      "id": 3,
      "name": "Skin Care",
      "slug": "skin-care",
      "description": "description for skin care category"
    }
  ]
}
```

### Delete Product

**Endpoint:**
```
DELETE /api/store/products/:id
```

**Example Request:**
```bash
curl -X DELETE http://localhost:3300/api/store/products/2
```

**Example Response:**
```json
{
  "id": 2,
  "createdAt": "2025-04-26T15:30:00.000Z",
  "updatedAt": "2025-04-26T15:30:00.000Z",
  "sku": "prod-001",
  "name": "Product Name",
  "description": "Product description",
  "shortDescription": "Short description",
  "price": "99.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": 100,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "SIMPLE",
  "images": [],
  "categories": []
}
```

## GROUPED Product APIs

GROUPED products allow you to create products with multiple attributes, attribute values, and variants.

### Create GROUPED Product

**Endpoint:**
```
POST /api/store/products
```

**Example Request:**
```bash
curl -X POST http://localhost:3300/api/store/products \
  -H "Content-Type: application/json" \
  -d '{
  "sku": "TSHIRT-GROUP",
  "name": "Premium T-Shirt Collection",
  "description": "Our premium t-shirt collection with multiple color and size options",
  "shortDescription": "Premium t-shirts in various colors and sizes",
  "price": "29.99",
  "stockStatus": "IN_STOCK",
  "type": "GROUPED",
  "access": "PRIVATE",
  "password": "secret-password",
  "categoryIds": [1, 2],
  "images": [
    {
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 0
    }
  ],
  "productAttributes": [
    {
      "attributeId": 1,
      "values": [
        {
          "value": "Small",
          "priceModifier": "0.00"
        },
        {
          "value": "Medium",
          "priceModifier": "0.00"
        },
        {
          "value": "Large",
          "priceModifier": "2.00"
        }
      ]
    },
    {
      "attributeId": 2,
      "values": [
        {
          "value": "Red",
          "priceModifier": "0.00"
        },
        {
          "value": "Blue",
          "priceModifier": "0.00"
        },
        {
          "value": "Black",
          "priceModifier": "0.00"
        }
      ]
    }
  ],
  "variants": [
    {
      "sku": "TSHIRT-S-RED",
      "price": "29.99",
      "stockQuantity": 50,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [1, 4],
      "images": [
        {
          "url": "https://example.com/images/tshirt-s-red.jpg",
          "position": 0
        }
      ]
    },
    {
      "sku": "TSHIRT-M-BLUE",
      "price": "29.99",
      "stockQuantity": 45,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [2, 5],
      "images": [
        {
          "url": "https://example.com/images/tshirt-m-blue.jpg",
          "position": 0
        }
      ]
    },
    {
      "sku": "TSHIRT-L-BLACK",
      "price": "31.99",
      "stockQuantity": 30,
      "stockStatus": "IN_STOCK",
      "attributeValueIds": [3, 6],
      "images": [
        {
          "url": "https://example.com/images/tshirt-l-black.jpg",
          "position": 0
        }
      ]
    }
  ]
}'
```

**Example Response:**
```json
{
  "id": 4,
  "createdAt": "2025-04-26T16:30:00.000Z",
  "updatedAt": "2025-04-26T16:30:00.000Z",
  "sku": "TSHIRT-GROUP",
  "name": "Premium T-Shirt Collection",
  "description": "Our premium t-shirt collection with multiple color and size options",
  "shortDescription": "Premium t-shirts in various colors and sizes",
  "price": "29.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": null,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "access": "PUBLIC",
  "password": null,
  "images": [
    {
      "id": 5,
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 0,
      "productId": 4,
      "variantId": null
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Clothing",
      "slug": "clothing",
      "description": "All clothing items"
    },
    {
      "id": 2,
      "name": "T-Shirts",
      "slug": "t-shirts",
      "description": "T-shirt collection"
    }
  ],
  "ProductAttribute": [
    {
      "id": 1,
      "productId": 4,
      "attributeId": 1,
      "attribute": {
        "id": 1,
        "name": "Size",
        "type": "PRIMARY"
      },
      "values": [
        {
          "id": 1,
          "productAttributeId": 1,
          "value": "Small",
          "priceModifier": "0.00"
        },
        {
          "id": 2,
          "productAttributeId": 1,
          "value": "Medium",
          "priceModifier": "0.00"
        },
        {
          "id": 3,
          "productAttributeId": 1,
          "value": "Large",
          "priceModifier": "2.00"
        }
      ]
    },
    {
      "id": 2,
      "productId": 4,
      "attributeId": 2,
      "attribute": {
        "id": 2,
        "name": "Color",
        "type": "SECONDARY"
      },
      "values": [
        {
          "id": 4,
          "productAttributeId": 2,
          "value": "Red",
          "priceModifier": "0.00"
        },
        {
          "id": 5,
          "productAttributeId": 2,
          "value": "Blue",
          "priceModifier": "0.00"
        },
        {
          "id": 6,
          "productAttributeId": 2,
          "value": "Black",
          "priceModifier": "0.00"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "sku": "TSHIRT-S-RED",
      "price": "29.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 50,
      "stockStatus": "IN_STOCK",
      "productId": 4,
      "ProductImage": [
        {
          "id": 6,
          "url": "https://example.com/images/tshirt-s-red.jpg",
          "position": 0,
          "productId": null,
          "variantId": 1
        }
      ],
      "attributes": [
        {
          "variantId": 1,
          "valueId": 1,
          "value": {
            "id": 1,
            "value": "Small",
            "priceModifier": "0.00"
          }
        },
        {
          "variantId": 1,
          "valueId": 4,
          "value": {
            "id": 4,
            "value": "Red",
            "priceModifier": "0.00"
          }
        }
      ]
    },
    {
      "id": 2,
      "sku": "TSHIRT-M-BLUE",
      "price": "29.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 45,
      "stockStatus": "IN_STOCK",
      "productId": 4,
      "ProductImage": [
        {
          "id": 7,
          "url": "https://example.com/images/tshirt-m-blue.jpg",
          "position": 0,
          "productId": null,
          "variantId": 2
        }
      ],
      "attributes": [
        {
          "variantId": 2,
          "valueId": 2,
          "value": {
            "id": 2,
            "value": "Medium",
            "priceModifier": "0.00"
          }
        },
        {
          "variantId": 2,
          "valueId": 5,
          "value": {
            "id": 5,
            "value": "Blue",
            "priceModifier": "0.00"
          }
        }
      ]
    },
    {
      "id": 3,
      "sku": "TSHIRT-L-BLACK",
      "price": "31.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 30,
      "stockStatus": "IN_STOCK",
      "productId": 4,
      "ProductImage": [
        {
          "id": 8,
          "url": "https://example.com/images/tshirt-l-black.jpg",
          "position": 0,
          "productId": null,
          "variantId": 3
        }
      ],
      "attributes": [
        {
          "variantId": 3,
          "valueId": 3,
          "value": {
            "id": 3,
            "value": "Large",
            "priceModifier": "2.00"
          }
        },
        {
          "variantId": 3,
          "valueId": 6,
          "value": {
            "id": 6,
            "value": "Black",
            "priceModifier": "0.00"
          }
        }
      ]
    }
  ]
}
```

### Update GROUPED Product

**Endpoint:**
```
PATCH /api/store/products/:id
```

**Example Request:**
```bash
curl -X PATCH http://localhost:3300/api/store/products/4 \
  -H "Content-Type: application/json" \
  -d '{
  "name": "Premium T-Shirt Collection 2023",
  "price": "34.99",
  "productAttributes": [
    {
      "attributeId": 3,
      "values": [
        {
          "value": "Cotton",
          "priceModifier": "0.00"
        },
        {
          "value": "Polyester",
          "priceModifier": "-5.00"
        }
      ]
    }
  ],
  "variants": [
    {
      "sku": "TSHIRT-S-RED-COTTON",
      "price": "34.99",
      "stockQuantity": 25,
      "attributeValueIds": [1, 4, 7],
      "images": [
        {
          "url": "https://example.com/images/tshirt-s-red-cotton.jpg",
          "position": 0
        }
      ]
    }
  ],
  "deleteVariantIds": [2],
  "deleteProductAttributeIds": []
}'
```

**Example Response:**
```json
{
  "id": 4,
  "createdAt": "2025-04-26T16:30:00.000Z",
  "updatedAt": "2025-04-26T16:45:00.000Z",
  "sku": "TSHIRT-GROUP",
  "name": "Premium T-Shirt Collection 2023",
  "description": "Our premium t-shirt collection with multiple color and size options",
  "shortDescription": "Premium t-shirts in various colors and sizes",
  "price": "34.99",
  "salePrice": null,
  "saleStart": null,
  "saleEnd": null,
  "stockQuantity": null,
  "stockStatus": "IN_STOCK",
  "taxStatus": "TAXABLE",
  "taxClass": "STANDARD",
  "type": "GROUPED",
  "images": [
    {
      "id": 5,
      "url": "https://example.com/images/tshirt-main.jpg",
      "position": 0,
      "productId": 4,
      "variantId": null
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "Clothing",
      "slug": "clothing",
      "description": "All clothing items"
    },
    {
      "id": 2,
      "name": "T-Shirts",
      "slug": "t-shirts",
      "description": "T-shirt collection"
    }
  ],
  "ProductAttribute": [
    {
      "id": 1,
      "productId": 4,
      "attributeId": 1,
      "attribute": {
        "id": 1,
        "name": "Size",
        "type": "PRIMARY"
      },
      "values": [
        {
          "id": 1,
          "productAttributeId": 1,
          "value": "Small",
          "priceModifier": "0.00"
        },
        {
          "id": 2,
          "productAttributeId": 1,
          "value": "Medium",
          "priceModifier": "0.00"
        },
        {
          "id": 3,
          "productAttributeId": 1,
          "value": "Large",
          "priceModifier": "2.00"
        }
      ]
    },
    {
      "id": 2,
      "productId": 4,
      "attributeId": 2,
      "attribute": {
        "id": 2,
        "name": "Color",
        "type": "SECONDARY"
      },
      "values": [
        {
          "id": 4,
          "productAttributeId": 2,
          "value": "Red",
          "priceModifier": "0.00"
        },
        {
          "id": 5,
          "productAttributeId": 2,
          "value": "Blue",
          "priceModifier": "0.00"
        },
        {
          "id": 6,
          "productAttributeId": 2,
          "value": "Black",
          "priceModifier": "0.00"
        }
      ]
    },
    {
      "id": 3,
      "productId": 4,
      "attributeId": 3,
      "attribute": {
        "id": 3,
        "name": "Material",
        "type": "SECONDARY"
      },
      "values": [
        {
          "id": 7,
          "productAttributeId": 3,
          "value": "Cotton",
          "priceModifier": "0.00"
        },
        {
          "id": 8,
          "productAttributeId": 3,
          "value": "Polyester",
          "priceModifier": "-5.00"
        }
      ]
    }
  ],
  "variants": [
    {
      "id": 1,
      "sku": "TSHIRT-S-RED",
      "price": "29.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 50,
      "stockStatus": "IN_STOCK",
      "productId": 4,
      "ProductImage": [
        {
          "id": 6,
          "url": "https://example.com/images/tshirt-s-red.jpg",
          "position": 0,
          "productId": null,
          "variantId": 1
        }
      ],
      "attributes": [
        {
          "variantId": 1,
          "valueId": 1,
          "value": {
            "id": 1,
            "value": "Small",
            "priceModifier": "0.00"
          }
        },
        {
          "variantId": 1,
          "valueId": 4,
          "value": {
            "id": 4,
            "value": "Red",
            "priceModifier": "0.00"
          }
        }
      ]
    },
    {
      "id": 3,
      "sku": "TSHIRT-L-BLACK",
      "price": "31.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 30,
      "stockStatus": "IN_STOCK",
      "productId": 4,
      "ProductImage": [
        {
          "id": 8,
          "url": "https://example.com/images/tshirt-l-black.jpg",
          "position": 0,
          "productId": null,
          "variantId": 3
        }
      ],
      "attributes": [
        {
          "variantId": 3,
          "valueId": 3,
          "value": {
            "id": 3,
            "value": "Large",
            "priceModifier": "2.00"
          }
        },
        {
          "variantId": 3,
          "valueId": 6,
          "value": {
            "id": 6,
            "value": "Black",
            "priceModifier": "0.00"
          }
        }
      ]
    },
    {
      "id": 4,
      "sku": "TSHIRT-S-RED-COTTON",
      "price": "34.99",
      "salePrice": null,
      "saleStart": null,
      "saleEnd": null,
      "stockQuantity": 25,
      "stockStatus": "IN_STOCK",
      "productId": 4,
      "ProductImage": [
        {
          "id": 9,
          "url": "https://example.com/images/tshirt-s-red-cotton.jpg",
          "position": 0,
          "productId": null,
          "variantId": 4
        }
      ],
      "attributes": [
        {
          "variantId": 4,
          "valueId": 1,
          "value": {
            "id": 1,
            "value": "Small",
            "priceModifier": "0.00"
          }
        },
        {
          "variantId": 4,
          "valueId": 4,
          "value": {
            "id": 4,
            "value": "Red",
            "priceModifier": "0.00"
          }
        },
        {
          "variantId": 4,
          "valueId": 7,
          "value": {
            "id": 7,
            "value": "Cotton",
            "priceModifier": "0.00"
          }
        }
      ]
    }
  ]
}
```

### Get GROUPED Product by ID

The existing GET product endpoint will automatically include product attributes, attribute values, and variants when the product type is GROUPED.

**Endpoint:**
```
GET /api/store/products/:id
```

### Delete GROUPED Product

The existing DELETE product endpoint will automatically handle the deletion of all related entities (attributes, attribute values, variants) when the product type is GROUPED.

**Endpoint:**
```
DELETE /api/store/products/:id
```

### Get Attribute Values

Retrieve all values for a specific attribute.

**Endpoint:**
```
GET /api/store/grouped-products/attributes/:id/values
```

**Example Request:**
```bash
curl -X GET http://localhost:3300/api/store/grouped-products/attributes/1/values
```

**Example Response:**
```json
[
  {
    "id": 1,
    "value": "Small",
    "priceModifier": "0.00"
  },
  {
    "id": 2,
    "value": "Medium",
    "priceModifier": "0.00"
  },
  {
    "id": 3,
    "value": "Large",
    "priceModifier": "2.00"
  }
]
```

## Frontend Configuration

For your frontend to work correctly with these endpoints, you need to update your API service to include the `/api` prefix. Here's how you might configure it:

```javascript
// Example API service configuration
const API_BASE_URL = 'http://localhost:3300/api';

// Example function to get all products
async function getAllProducts() {
  const response = await fetch(`${API_BASE_URL}/store/products`);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return await response.json();
}

// Example function to get products by category
async function getProductsByCategory(categoryId) {
  const response = await fetch(`${API_BASE_URL}/store/categories/${categoryId}/products`);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return await response.json();
}
```

Make sure your frontend is configured to use the correct base URL with the `/api` prefix for all API calls.
